{
  "// ===================================================================",
  "// 🌟 VIBECODE V5.0 - GitHub Copilot Enhanced Configuration",
  "// Consolidated MCP + Desktop Commander + NeonPro Settings",
  "// Quality: ≥9.8/10 | MCP Integration: 100% | Enhancement: Real-time",
  "// ===================================================================",
  
  "// 🚀 CORE ENHANCEMENT PROTOCOLS": "Real-time GitHub Copilot suggestion enhancement",
  "github.copilot.chat.enhancement.enabled": true,
  "github.copilot.chat.enhancement.quality.threshold": 9.8,
  "github.copilot.chat.enhancement.confidence.minimum": 95,
  "github.copilot.chat.enhancement.research.required": true,
  "github.copilot.chat.enhancement.mcp.validation": true,
  
  "// 🛡️ MCP ENFORCEMENT PROTOCOLS": "Mandatory MCP usage for all operations",
  "mcp.enforcement.enabled": true,
  "mcp.enforcement.level": "APEX_ABSOLUTE",
  "mcp.enforcement.tolerance": "zero",
  "mcp.enforcement.file_operations": "mandatory_desktop_commander",
  "mcp.enforcement.terminal_operations": "mandatory_desktop_commander",
  "mcp.enforcement.research_operations": "mandatory_context7_tavily_exa",
  "mcp.enforcement.quality_gates": true,
  "mcp.enforcement.auto_correction": true,
  
  "// 🔄 DESKTOP COMMANDER INTEGRATION": "Complete file and terminal operation control",
  "desktop_commander.enabled": true,
  "desktop_commander.file_operations": {
    "read_file": "mcp_desktop-comma_read_file",
    "write_file": "mcp_desktop-comma_write_file", 
    "edit_block": "mcp_desktop-comma_edit_block",
    "list_directory": "mcp_desktop-comma_list_directory",
    "create_directory": "mcp_desktop-comma_create_directory",
    "move_file": "mcp_desktop-comma_move_file",
    "search_files": "mcp_desktop-comma_search_files",
    "search_code": "mcp_desktop-comma_search_code"
  },
  "desktop_commander.terminal_operations": {
    "start_process": "mcp_desktop-comma_start_process",
    "interact_with_process": "mcp_desktop-comma_interact_with_process",
    "read_process_output": "mcp_desktop-comma_read_process_output",
    "list_processes": "mcp_desktop-comma_list_processes",
    "kill_process": "mcp_desktop-comma_kill_process",
    "list_sessions": "mcp_desktop-comma_list_sessions"
  },
  "desktop_commander.validation": {
    "directory_verification": "mandatory",
    "chunked_operations": "50_lines_maximum",
    "error_handling": "comprehensive",
    "performance_monitoring": "enabled"
  },
  
  "// 🧠 RESEARCH INTEGRATION": "Multi-MCP research chain for complex tasks",
  "research.protocols.enabled": true,
  "research.sequence": ["context7", "tavily", "exa", "sequential_thinking"],
  "research.auto_triggers": {
    "complexity_threshold": 5,
    "keywords": ["research", "investigate", "analyze", "implement", "architecture"],
    "documentation_validation": true,
    "expert_patterns": true
  },
  "research.context7": {
    "auto_invoke": true,
    "library_detection": true,
    "version_specific": true,
    "real_time_docs": true
  },
  
  "// 📊 PERFORMANCE OPTIMIZATION": "85%+ context reduction with quality preservation",
  "performance.context_reduction": 85,
  "performance.smart_mcp_routing": true,
  "performance.intelligent_bypass": true,
  "performance.cache_optimization": true,
  "performance.response_time_target": "150ms",
  "performance.execution_time_target": "30s",
  
  "// 💾 MEMORY BANK INTEGRATION": "RooCode compatible smart memory system",
  "memory_bank.enabled": true,
  "memory_bank.smart_activation": true,
  "memory_bank.portuguese_triggers": ["lembre-se", "continue", "implementar", "debug"],
  "memory_bank.english_triggers": ["remember", "continue", "implement", "debug"],
  "memory_bank.skip_conditions": ["what", "how", "explain", "define"],
  "memory_bank.files": {
    "activeContext": "memory-bank/activeContext.md",
    "decisionLog": "memory-bank/decisionLog.md",
    "progress": "memory-bank/progress.md",
    "systemPatterns": "memory-bank/systemPatterns.md",
    "techContext": "memory-bank/techContext.md",
    "projectbrief": "memory-bank/projectbrief.md"
  },  
  "// 🎯 INTELLIGENT ROUTING": "Complexity-based MCP chain selection",
  "routing.complexity_detection": true,
  "routing.levels": {
    "L1_simple": "1.0-3.0",
    "L2_moderate": "3.1-5.5", 
    "L3_complex": "5.6-7.5",
    "L4_enterprise": "7.6-10.0"
  },
  "routing.mcp_chains": {
    "simple": ["desktop_commander"],
    "moderate": ["context7", "desktop_commander"],
    "complex": ["context7", "tavily", "sequential_thinking", "desktop_commander"],
    "enterprise": ["context7", "tavily", "exa", "sequential_thinking", "desktop_commander"]
  },
  
  "// 🔒 QUALITY ENFORCEMENT": "Multi-layer validation system",
  "quality.threshold": 9.8,
  "quality.confidence_minimum": 95,
  "quality.validation_layers": [
    "syntax_validation",
    "security_validation", 
    "performance_validation",
    "accessibility_validation",
    "compliance_validation"
  ],
  "quality.auto_enhancement": true,
  "quality.continuous_monitoring": true,
  
  "// 🏥 NEONPRO HEALTHCARE INTEGRATION": "Medical software compliance",
  "neonpro.enabled": true,
  "neonpro.compliance": {
    "lgpd": true,
    "anvisa": true,
    "cfm": true,
    "audit_logging": true,
    "data_encryption": true
  },
  "neonpro.workflow": {
    "patient_data_protection": true,
    "medical_records_security": true,
    "appointment_privacy": true,
    "billing_compliance": true
  },
  
  "// 📝 ENHANCED CODE GENERATION": "Professional development standards",
  "github.copilot.chat.codeGeneration.instructions": [
    "🎯 CODE GENERATION EXCELLENCE (≥9.8/10 Quality):",
    "",
    "🏗️ ARCHITECTURE PATTERNS:",
    "- Next.js 15 App Router: Use Server Components by default, Client Components only when needed",
    "- TypeScript First: Always use TypeScript with strict configuration and proper typing",
    "- Component Patterns: Follow shadcn/ui patterns with Radix UI primitives",
    "- Database Security: Always implement Row Level Security (RLS) for Supabase operations",
    "- Authentication: Use dual client pattern (@/utils/supabase/client and server)",
    "",
    "🔐 SECURITY & COMPLIANCE:",
    "- Healthcare Data: Follow LGPD, ANVISA, and CFM regulations for patient data",
    "- Input Validation: Use Zod schemas for all form inputs and API validation",
    "- SQL Injection Prevention: Use parameterized queries and Supabase client methods",
    "- XSS Protection: Sanitize all user inputs and use proper escaping",
    "- CSRF Protection: Implement CSRF tokens for state-changing operations",
    "",
    "🚀 PERFORMANCE OPTIMIZATION:",
    "- Image Optimization: Use next/image with proper sizing and lazy loading",
    "- Font Optimization: Use next/font with preloading and display optimization",
    "- Bundle Analysis: Code splitting and dynamic imports for large components",
    "- Caching Strategy: Implement proper ISR, SSG, and client-side caching",
    "- Database Queries: Optimize queries with proper indexing and select statements",
    "",
    "🎨 UI/UX STANDARDS:",
    "- Accessibility: WCAG 2.1 AA compliance with proper ARIA labels and keyboard navigation",
    "- Responsive Design: Mobile-first approach with proper breakpoints",
    "- Loading States: Implement skeleton screens and loading indicators",
    "- Error Handling: User-friendly error messages with recovery options",
    "- Healthcare UX: Optimize for clinical workflows and medical professional needs",
    "",
    "📦 COMPONENT DEVELOPMENT:",
    "- Reusability: Create composable components following single responsibility principle",
    "- Props Interface: Define clear TypeScript interfaces for all component props",
    "- Error Boundaries: Implement proper error boundaries for fault tolerance",
    "- Testing: Include unit tests with Testing Library for all components",
    "- Documentation: Add JSDoc comments for complex components and hooks"
  ],
  
  "// 🧪 TESTING STANDARDS": "Comprehensive testing strategy",
  "github.copilot.chat.testing.instructions": [
    "🧪 TESTING EXCELLENCE STANDARDS:",
    "",
    "📋 TESTING STRATEGY:",
    "- Unit Tests: Use Testing Library for components with proper setup/teardown",
    "- Integration Tests: Test API routes and database operations with realistic mock data",
    "- E2E Tests: Cover critical user flows using Cypress with healthcare workflow scenarios", 
    "- Healthcare Testing: Use realistic but fake patient data, test compliance scenarios",
    "",
    "🧪 TEST STRUCTURE REQUIREMENTS:",
    "- Arrange-Act-Assert: Follow clear test organization pattern",
    "- Descriptive Names: Test names should explain the scenario being validated",
    "- Edge Cases: Include error scenarios and boundary conditions",
    "- Mock Strategy: Mock external dependencies appropriately (Supabase, APIs)",
    "- Cleanup: Ensure proper test isolation and cleanup between tests"
  ],  
  "// 📝 COMMIT MESSAGE STANDARDS": "Conventional Commits with healthcare context",
  "github.copilot.chat.commitMessageGeneration.instructions": [
    "📦 COMMIT MESSAGE EXCELLENCE:",
    "- Format: type(scope): description",
    "- Types: feat, fix, docs, style, refactor, test, chore, security, compliance",
    "- Scope: Include relevant feature area (auth, patients, appointments, billing, etc.)",
    "- Description: Use imperative mood, keep first line under 50 characters",
    "- Healthcare Context: Include compliance notes for patient data changes",
    "- Body: Include detailed explanation for complex changes",
    "- Footer: Reference issues and breaking changes"
  ],
  
  "// 🔍 CODE REVIEW STANDARDS": "Professional review excellence",
  "github.copilot.chat.codeReview.instructions": [
    "🔍 CODE REVIEW EXCELLENCE (≥9.8/10 Quality):",
    "",
    "🏗️ ARCHITECTURE REVIEW:",
    "- Pattern Adherence: Verify compliance with established architectural patterns",
    "- SOLID Principles: Check for single responsibility, open/closed, dependency inversion",
    "- Component Design: Ensure proper separation of concerns and reusability",
    "- Data Flow: Validate proper state management and data flow patterns",
    "",
    "🔐 SECURITY REVIEW:",
    "- Authentication: Verify proper auth implementation and session management", 
    "- Authorization: Check RLS policies and permission-based access control",
    "- Data Protection: Validate encryption, sanitization, and LGPD compliance",
    "- Input Validation: Ensure comprehensive validation and sanitization",
    "",
    "🚀 PERFORMANCE REVIEW:",
    "- Query Optimization: Check database queries for efficiency and indexing",
    "- Bundle Analysis: Verify code splitting and import optimization",
    "- Caching Strategy: Validate caching implementation and invalidation",
    "- Resource Usage: Check for memory leaks and optimal resource utilization",
    "",
    "🎨 UX/ACCESSIBILITY REVIEW:",
    "- WCAG Compliance: Verify WCAG 2.1 AA accessibility standards",
    "- Responsive Design: Test across different screen sizes and devices",
    "- Loading States: Ensure proper loading indicators and skeleton screens",
    "- Error Handling: Validate user-friendly error messages and recovery",
    "",
    "🏥 HEALTHCARE COMPLIANCE REVIEW:",
    "- Patient Privacy: Verify LGPD and healthcare regulation compliance",
    "- Audit Trails: Check logging and audit trail implementation",
    "- Data Retention: Validate data retention and deletion policies",
    "- Medical Workflows: Ensure clinical workflow optimization"
  ],
  
  "// 🤖 AGENT CONFIGURATION": "Enhanced AI capabilities",
  "chat.agent.enabled": true,
  "chat.agent.maxRequests": 50,
  "chat.agent.enhancement.realtime": true,
  "github.copilot.chat.agent.autoFix": true,
  "github.copilot.chat.agent.runTasks": true,
  "github.copilot.chat.agent.followUp": true,
  "github.copilot.chat.agent.qualityThreshold": 9.8,
  "github.copilot.chat.agent.confidenceMinimum": 95,
  
  "// 💬 ADVANCED CHAT FEATURES": "Enhanced interaction capabilities",
  "chat.mcp.enabled": true,
  "chat.mcp.auto_routing": true,
  "chat.implicitContext.enabled": true,
  "chat.contextual.memory": true,
  "github.copilot.chat.edits.temporalContext.enabled": true,
  "github.copilot.chat.localeOverride": "pt-BR",
  "github.copilot.chat.useProjectContext": true,
  "github.copilot.chat.useFileContext": true,
  "github.copilot.chat.useGitContext": true,
  
  "// ⚡ EDITOR INTEGRATION": "Seamless development experience",
  "github.copilot.editor.enableAutoCompletions": true,
  "github.copilot.editor.enableCodeActions": true,
  "github.copilot.editor.enhancement.enabled": true,
  "github.copilot.chat.experimental.codeGeneration.instructions.enabled": true,
  "github.copilot.editor.suggestionDelay": 10,
  "github.copilot.editor.inlineSuggest.enabled": true,  
  "// 👥 PARTICIPANT CONFIGURATION": "Specialized chat participants",
  "github.copilot.chat.participants": {
    "@vscode": {
      "description": "VS Code and extension help with enhanced MCP integration",
      "enabled": true,
      "mcp_enhanced": true
    },
    "@terminal": {
      "description": "Terminal and shell commands via Desktop Commander MCP",
      "enabled": true,
      "mcp_routing": "desktop_commander",
      "auto_enhancement": true
    },
    "@workspace": {
      "description": "Workspace and project context with memory bank integration",
      "enabled": true,
      "memory_bank_integration": true,
      "context_optimization": true
    },
    "@research": {
      "description": "Research and documentation via Context7, Tavily, Exa MCPs",
      "enabled": true,
      "mcp_chain": ["context7", "tavily", "exa"],
      "auto_validation": true
    }
  },
  
  "// 🎯 LANGUAGE PREFERENCES": "Technology-specific optimization",
  "github.copilot.chat.codeGeneration.languagePreferences": {
    "typescript": "prefer",
    "javascript": "avoid_unless_required",
    "tsx": "prefer",
    "jsx": "avoid_unless_required",
    "sql": "use_supabase_client",
    "python": "use_for_automation_only",
    "markdown": "mandatory_for_documentation"
  },
  
  "// ⚡ PERFORMANCE OPTIMIZATION": "Advanced response configuration",
  "github.copilot.advanced": {
    "length": 2000,
    "temperature": 0.1,
    "top_p": 0.1,
    "inlineSuggestCount": 5,
    "listCount": 15,
    "enhancementEnabled": true,
    "mcpIntegration": true,
    "contextOptimization": true
  },
  
  "// 🛡️ QUALITY ASSURANCE": "Automated quality checks",
  "github.copilot.chat.quality": {
    "enableSyntaxCheck": true,
    "enableStyleCheck": true,
    "enableSecurityCheck": true,
    "enablePerformanceCheck": true,
    "enableAccessibilityCheck": true,
    "enableComplianceCheck": true,
    "qualityThreshold": 9.8,
    "autoEnhancement": true,
    "mcpValidation": true
  },
  
  "// 🏥 HEALTHCARE COMPLIANCE": "Medical data protection and regulations",
  "github.copilot.chat.compliance": {
    "lgpd": {
      "enabled": true,
      "dataMinimization": true,
      "consentManagement": true,
      "rightToErasure": true,
      "auditLogging": true
    },
    "anvisa": {
      "enabled": true,
      "medicalSoftwareClassification": true,
      "regulatoryCompliance": true,
      "qualityManagement": true
    },
    "cfm": {
      "enabled": true,
      "medicalPracticeStandards": true,
      "patientConfidentiality": true,
      "professionalEthics": true
    },
    "international": {
      "hipaa": false,
      "gdpr": true,
      "iso27001": true,
      "iso13485": true
    }
  },  
  "// 🔧 DEVELOPMENT WORKFLOW": "Enhanced development experience",
  "workflow": {
    "auto_enhancement": true,
    "quality_gates": true,
    "mcp_integration": "mandatory",
    "research_validation": true,
    "memory_bank_consultation": true,
    "continuous_improvement": true
  },
  
  "// 📊 MONITORING & ANALYTICS": "Performance and usage tracking",
  "monitoring": {
    "performance_metrics": true,
    "quality_tracking": true,
    "mcp_usage_analytics": true,
    "enhancement_effectiveness": true,
    "user_satisfaction": true,
    "compliance_monitoring": true
  },
  
  "// 🎨 UI/UX OPTIMIZATION": "Enhanced user experience",
  "ui_ux": {
    "accessibility_first": true,
    "wcag_compliance": "AA",
    "clinical_workflow_optimization": true,
    "mobile_responsive": true,
    "dark_mode_support": true,
    "internationalization": "pt-BR"
  },
  
  "// 🔄 AUTO-UPDATE CONFIGURATION": "Intelligent system updates",
  "auto_update": {
    "enabled": true,
    "mcp_protocols": true,
    "quality_standards": true,
    "compliance_requirements": true,
    "performance_optimizations": true,
    "security_patches": true
  },
  
  "// 📚 DOCUMENTATION STANDARDS": "Comprehensive documentation requirements",
  "documentation": {
    "format": "markdown_mandatory",
    "api_documentation": "openapi_swagger",
    "code_comments": "jsdoc_required",
    "architecture_decisions": "adr_format",
    "compliance_documentation": "regulatory_required",
    "user_guides": "healthcare_focused"
  },
  
  "// 🚀 DEPLOYMENT & DEVOPS": "Production-ready deployment",
  "deployment": {
    "environment_validation": true,
    "security_scanning": true,
    "performance_testing": true,
    "compliance_verification": true,
    "rollback_strategy": true,
    "monitoring_integration": true
  },
  
  "// 🔐 SECURITY HARDENING": "Advanced security measures",
  "security": {
    "code_scanning": true,
    "dependency_checking": true,
    "secret_scanning": true,
    "vulnerability_assessment": true,
    "penetration_testing": false,
    "compliance_auditing": true
  },
  
  "// 📈 PERFORMANCE TARGETS": "Measurable performance goals",
  "performance_targets": {
    "code_quality": "≥9.8/10",
    "test_coverage": "≥90%",
    "accessibility_score": "≥95%", 
    "performance_score": "≥90%",
    "security_score": "≥95%",
    "compliance_score": "100%"
  }
}