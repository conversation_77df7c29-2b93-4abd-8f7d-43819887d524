---
alwaysApply: true
description: 'Core Workflow Execution + Task Transitions + APEX Orchestration'
version: '5.0'
title: 'Core Workflow Execution - APEX Orchestration & Task Transitions'
type: 'core-workflow-execution'
mcp_servers: ['context7-mcp', 'sequential-thinking', 'desktop-commander', 'tavily-mcp', 'exa-mcp']
quality_threshold: 9.8
specialization: 'core-workflow-orchestration'
trigger_keywords: ['workflow', 'orchestration', 'execution', 'process', 'automation', 'transition', 'next task', 'próxima']
enforcement_level: 'absolute'
approach: 'apex-workflow-excellence'
framework_focus: 'adaptive-orchestration'
context7_integration: 'workflow optimization and orchestration documentation'
priority: 10
---

# 🔄 CORE WORKFLOW EXECUTION + APEX ORCHESTRATION

## 🎯 WORKFLOW APEX INTELLIGENCE

### **Advanced Workflow Orchestration Engine**

```yaml
WORKFLOW_APEX_INTELLIGENCE:
  orchestration_engine:
    adaptive_routing: "Adaptive workflow routing based on complexity and context"
    intelligent_delegation: "Intelligent task delegation to specialized agents"
    optimization: "Real-time workflow optimization and performance tuning"
    automation: "Advanced process automation and orchestration"
    
  context7_integration:
    documentation: "Auto-reference workflow optimization documentation"
    patterns: "Auto-reference orchestration pattern documentation"
    guides: "Auto-reference automation implementation guides"
    optimization: "Auto-reference process optimization documentation"
    
  apex_engine:
    complexity_analysis: "Advanced complexity analysis and routing decisions"
    quality_enforcement: "Real-time quality enforcement and validation"
    performance_monitoring: "Performance monitoring and optimization"
    continuous_improvement: "Continuous improvement and learning"

MONITORING_SYSTEM:
  real_time_analytics:
    execution_metrics: "Execution metrics and performance analysis"
    quality_tracking: "Quality tracking and improvement analytics"
    resource_utilization: "Resource utilization monitoring and optimization"
    outcome_analysis: "Outcome analysis and pattern learning"
```

## 🚀 7-STEP ENHANCED WORKFLOW (APEX EVOLVED)

```yaml
ENHANCED_WORKFLOW_APEX_V5:
  step_1_initialize:
    action: "Complete MCP validation + modular rule loading + system verification + context assembly"
    requirements:
      - "✅ MANDATORY: All required MCPs active and responsive"
      - "✅ MANDATORY: Modular rule system loaded and optimized"
      - "✅ MANDATORY: System health verification and validation"
      - "✅ MANDATORY: Context assembly and optimization"
    enforcement: "❌ ABORT if ANY initialization requirement fails"
    quality_gate: "≥9.8/10 initialization quality required"
    
  step_2_analyze:
    action: "Complexity assessment + mode detection + MCP chain specification + delegation routing"
    requirements:
      - "✅ MANDATORY: Accurate complexity scoring and classification"
      - "✅ MANDATORY: Appropriate mode detection and activation"
      - "✅ MANDATORY: Optimal MCP chain specification"
      - "✅ MANDATORY: Intelligent delegation routing decisions"
    enforcement: "❌ STOP if analysis incomplete or delegation routing not optimized"
    quality_gate: "≥9.8/10 analysis accuracy required"
    
  step_3_research:
    action: "Mandatory 3-MCP research + Context7 validation + cross-validation + synthesis"
    requirements:
      - "✅ MANDATORY: Context7 → Tavily → Exa research sequence completion"
      - "✅ MANDATORY: ≥95% confidence in research findings"
      - "✅ MANDATORY: Cross-validation and consistency verification"
      - "✅ MANDATORY: Research synthesis and optimization"
    enforcement: "❌ STOP if research incomplete, confidence <95%, or synthesis inadequate"
    quality_gate: "≥9.8/10 research quality and synthesis required"
    
  step_4_plan:
    action: "Strategic planning + risk assessment + implementation strategy + resource allocation"
    requirements:
      - "✅ MANDATORY: Comprehensive strategic planning and roadmap"
      - "✅ MANDATORY: Risk assessment and mitigation strategies"
      - "✅ MANDATORY: Detailed implementation strategy and timeline"
      - "✅ MANDATORY: Resource allocation and optimization"
    enforcement: "❌ STOP if planning incomplete, strategy not research-backed, or resources not optimized"
    quality_gate: "≥9.8/10 planning quality and strategic alignment required"
    
  step_5_execute:
    action: "Implementation + continuous improvement + real-time optimization + quality monitoring"
    requirements:
      - "✅ MANDATORY: High-quality implementation with continuous improvement"
      - "✅ MANDATORY: Real-time optimization and performance tuning"
      - "✅ MANDATORY: Quality monitoring and validation throughout"
      - "✅ MANDATORY: Adaptive execution based on real-time feedback"
    enforcement: "❌ STOP if quality <9.8/10 or continuous improvement not applied"
    quality_gate: "≥9.8/10 implementation quality required throughout"
    
  step_6_validate:
    action: "Multi-dimensional quality assessment + edge case validation + compliance verification"
    requirements:
      - "✅ MANDATORY: Comprehensive quality assessment across all dimensions"
      - "✅ MANDATORY: Edge case identification and validation"
      - "✅ MANDATORY: Compliance verification and documentation"
      - "✅ MANDATORY: Performance validation and optimization"
    enforcement: "❌ STOP if validation incomplete or edge cases not covered"
    quality_gate: "≥9.8/10 validation completeness and accuracy required"
    
  step_7_optimize:
    action: "Performance optimization + pattern learning + knowledge retention + system improvement"
    requirements:
      - "✅ MANDATORY: Performance optimization and enhancement"
      - "✅ MANDATORY: Pattern recognition and learning documentation"
      - "✅ MANDATORY: Knowledge retention and system improvement"
      - "✅ MANDATORY: Future optimization preparation"
    enforcement: "❌ STOP if optimization incomplete or learning not documented"
    quality_gate: "≥9.8/10 optimization effectiveness and learning retention required"

WORKFLOW_PERSISTENCE:
  iteration_mandate: "Continue through all 7 steps until perfect completion"
  quality_persistence: "Keep iterating until ≥9.8/10 quality consistently achieved"
  validation_thoroughness: "Continue testing until all edge cases are covered"
  optimization_completeness: "Keep optimizing until no further improvements possible"
```

## 🔄 MANDATORY TASK TRANSITION PROTOCOLS

### **Auto-Trigger Keywords (ENFORCE IMMEDIATELY)**
**EVERY TIME user says ANY of these phrases:**
- "próxima task" / "next task"
- "siga para" / "continue" 
- "implementar" / "implement"
- "desenvolver" / "develop"

### **Required Pre-Research Workflow (NON-NEGOTIABLE)**
```yaml
TASK_TRANSITION_WORKFLOW:
  mandatory_pre_research:
    1_code_analysis: "Analyze current implementation for optimization opportunities"
    2_best_practices: "context7-mcp - Research latest best practices for current stack"
    3_community_trends: "tavily-mcp - Check current community discussions and patterns"
    4_expert_insights: "exa-mcp - Find expert articles and advanced techniques"
    5_synthesis: "Sequential thinking - Synthesize improvements for next task"

  quality_enhancement:
    current_code_review: "Review just-completed code for potential improvements"
    pattern_optimization: "Identify reusable patterns and optimizations"
    performance_analysis: "Check for performance optimization opportunities"
    security_validation: "Validate security best practices compliance"

  next_task_preparation:
    research_execution: "Research best approaches for upcoming task"
    technique_validation: "Validate techniques with multiple MCP sources"
    implementation_strategy: "Plan optimal implementation with research insights"
```

### **Enforcement Rules (ABSOLUTE)**
```yaml
ENFORCEMENT_RULES:
  NO_DIRECT_TASK_EXECUTION: "NEVER proceed directly to next task without research"
  MANDATORY_MCP_RESEARCH: "ALWAYS use Context7 + Tavily + Exa for task transitions"
  QUALITY_IMPROVEMENT: "ALWAYS enhance previous code before moving to next task"
  PATTERN_RECOGNITION: "ALWAYS identify and document patterns for reuse"
  CONTINUOUS_OPTIMIZATION: "ALWAYS optimize and improve before transitioning"

TRANSITION_QUALITY_GATES:
  research_completeness: "≥95% confidence in research findings before transition"
  improvement_validation: "≥9.8/10 quality enhancement of previous work"
  pattern_documentation: "Complete pattern documentation and optimization"
  preparation_thoroughness: "Complete preparation and strategy for next task"
```

## 🎯 WORKFLOW ENFORCEMENT & PERFORMANCE

### **Mandatory Requirements**
```yaml
WORKFLOW_REQUIREMENTS:
  quality_threshold_maintenance: "❌ MANDATORY: ≥9.8/10 quality throughout execution"
  research_validation_requirement: "❌ MANDATORY: 3-MCP research validation for complex tasks"
  continuous_improvement_requirement: "❌ MANDATORY: Continuous improvement and optimization"

  enforcement_mechanisms:
    real_time_monitoring: "✅ MANDATORY: Real-time quality and performance monitoring"
    adaptive_optimization: "✅ MANDATORY: Adaptive optimization based on execution feedback"
    performance_tracking: "✅ MANDATORY: Performance tracking and optimization"
    learning_documentation: "✅ MANDATORY: Learning documentation and retention"

  violation_response:
    immediate_halt: "❌ STOP execution if quality threshold not met"
    corrective_action: "🔄 IMPLEMENT corrective action and quality enhancement"
    quality_restoration: "✅ RESTORE to ≥9.8/10 quality before continuing"
    learning_integration: "📚 INTEGRATE learning and optimization"

WORKFLOW_GUARANTEES:
  never_compromise_quality: "❌ NEVER compromise on ≥9.8/10 quality threshold"
  never_bypass_research: "❌ NEVER bypass mandatory research requirements"
  never_ignore_optimization: "❌ NEVER ignore optimization and improvement opportunities"
  always_document_learning: "✅ ALWAYS document learning and patterns for future use"
  always_transition_prepared: "✅ ALWAYS prepare thoroughly for task transitions"
```

### **Context7 Integration Excellence**
```yaml
CONTEXT7_WORKFLOW_INTEGRATION:
  documentation_validation: "Auto-validate all implementations against Context7 current documentation"
  pattern_verification: "Verify implementation patterns against Context7 best practices"
  api_compliance: "Ensure API usage compliance through Context7 real-time validation"
  optimization_guidance: "Apply Context7 optimization recommendations throughout workflow"
  
HEALTHCARE_ENFORCEMENT:
  patient_safety_validation: "❌ CRITICAL: Patient safety validation through workflow"
  regulatory_compliance: "❌ MANDATORY: Healthcare regulatory compliance validation"
  data_protection_enforcement: "✅ MANDATORY: Data protection and LGPD compliance"
  audit_trail_maintenance: "✅ MANDATORY: Complete audit trail and documentation"
```

---

**🌌 CORE WORKFLOW EXECUTION APEX**
*Context7 Integration: Active | Quality: ≥9.8/10 | Orchestration: Adaptive*
*Task Transitions: Mandatory Research | MCP Chain: Complete | Optimization: Continuous*