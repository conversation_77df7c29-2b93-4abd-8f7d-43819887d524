#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Auto-Integration Script - VIBECODE Memory Bank V5.0
    Atualiza todas as instructions e rules para usar o novo smart memory bank

.DESCRIPTION
    Script que automaticamente integra o novo memory bank roo-code-enhanced
    em todas as instructions, rules e workflows do VIBECODE

.EXAMPLE
    .\auto-integrate.ps1
    .\auto-integrate.ps1 -Validate
    .\auto-integrate.ps1 -UpdateOnly
#>

param(
    [switch]$Validate,
    [switch]$UpdateOnly,
    [switch]$DryRun
)

# Configuração
$WorkspaceRoot = Split-Path -Parent $PSScriptRoot
$MemoryBankPath = Join-Path $WorkspaceRoot "memory-bank"
$InstructionsPath = Join-Path $WorkspaceRoot ".github\instructions"
$RulesPath = Join-Path $WorkspaceRoot ".github\rules"

function Write-Header {
    Write-Host "🔧 VIBECODE Memory Bank V5.0 - Auto-Integration" -ForegroundColor Cyan
    Write-Host "📅 $((Get-Date).ToString('dd/MM/yyyy HH:mm:ss'))" -ForegroundColor Gray
    Write-Host "🎯 Integrando smart memory bank em todas as rules e instructions" -ForegroundColor Blue
    Write-Host ""
}

function Get-IntegrationTemplate {
    return @'
## 🧠 VIBECODE Memory Bank V5.0 Integration (Roo-Code Enhanced)

### **🚀 SMART MEMORY BANK ACTIVATION (PORTUGUESE TRIGGERS)**
```yaml
MEMORY_BANK_AUTO_TRIGGERS:
  portuguese_keywords: ["lembre-se", "não se esqueça", "implementar", "continuar", "neonpro", "debug", "otimizar"]
  english_keywords: ["remember", "implement", "continue", "develop", "debug", "optimize"]
  skip_conditions: ["o que", "what", "como", "how", "explique", "explain"]
  
  smart_loading:
    level_1_instant: "activeContext.md (sempre <50ms)"
    level_2_conditional: "progress.md, decisionLog.md (se relevante)"
    level_3_full: "systemPatterns.md, techContext.md (se complexo)"
    
  performance_targets:
    load_time: "<200ms"
    cache_hit_rate: ">90%"
    token_reduction: "85%+"
    zero_interruption: "100%"
```

### **📋 MEMORY BANK INTEGRATION PROTOCOL**
```yaml
INTEGRATION_WORKFLOW:
  detection_phase:
    - "Detect Portuguese/English trigger keywords in user query"
    - "Assess if memory bank consultation would add value"
    - "Determine optimal loading level (1-3) based on complexity"
    
  loading_phase:
    - "mcp_desktop-comma_read_file memory-bank/activeContext.md (if triggered)"
    - "mcp_desktop-comma_read_file memory-bank/progress.md (if continuation task)"
    - "mcp_desktop-comma_read_file memory-bank/systemPatterns.md (if pattern applicable)"
    - "mcp_desktop-comma_read_file memory-bank/decisionLog.md (if architectural decision)"
    
  application_phase:
    - "Apply memory bank context to current workflow"
    - "Use established patterns and decisions to optimize work"
    - "Document new learnings back to memory bank"
    
  efficiency_enforcement:
    - "❌ NO unnecessary memory loading for simple queries"
    - "✅ SMART activation only when value-adding"
    - "⚡ <200ms total load time target"
    - "🎯 >90% relevance threshold for loaded content"
```

### **🔄 MEMORY BANK UPDATE PROTOCOL**
```yaml
AUTO_UPDATE_TRIGGERS:
  immediate_updates:
    - "New technical decision with architectural impact"
    - "Successful pattern that could be reused"
    - "Error resolution that should be documented"
    - "Optimization or quality improvement implemented"
    
  update_commands:
    - "mcp_desktop-comma_write_file memory-bank/activeContext.md (current focus changes)"
    - "mcp_desktop-comma_write_file memory-bank/progress.md (milestone completion)"
    - "mcp_desktop-comma_write_file memory-bank/systemPatterns.md (new patterns discovered)"
    - "mcp_desktop-comma_write_file memory-bank/decisionLog.md (architectural decisions)"
    
  umb_command_integration:
    - "Use umb.ps1 or umb.js for manual memory bank operations"
    - "Auto-optimization via smart-memory.js background system"
    - "Real-time learning and pattern extraction"
```

### **🎯 NEONPRO PROJECT SPECIALIZATION**
```yaml
NEONPRO_MEMORY_INTEGRATION:
  auto_activation: "Keywords: neonpro, clínica, médico, saúde, LGPD, ANVISA"
  specialized_context:
    - "Healthcare compliance patterns (LGPD, ANVISA, CFM)"
    - "Next.js 15 + Supabase + TypeScript clinic management patterns"
    - "Quality threshold: ≥9.5/10 for medical systems"
    - "Performance: <200ms for medical UX requirements"
    
  bmad_integration:
    - "Story-driven development with memory bank context"
    - "Automatic context loading for task/story/epic execution"
    - "Memory-informed task delegation and specialization"
```

**ENFORCEMENT**: Every workflow must integrate smart memory bank triggers and optimization
**PERFORMANCE**: <200ms load time with 85%+ token reduction through intelligent loading
**QUALITY**: ≥8/10 information synthesis with automatic anti-hallucination protocols
'@
}

function Update-InstructionFile {
    param(
        [string]$FilePath,
        [string]$FileName
    )
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "⚠️  Arquivo não encontrado: $FileName" -ForegroundColor Yellow
        return
    }
    
    # Lê o conteúdo atual
    $Content = Get-Content $FilePath -Raw -Encoding UTF8
    
    # Verifica se já tem integração V5.0
    if ($Content -match "VIBECODE Memory Bank V5\.0") {
        Write-Host "✅ $FileName já tem integração V5.0" -ForegroundColor Green
        return
    }
    
    # Remove integrações antigas se existirem
    $Content = $Content -replace '(?ms)## 🧠 VIBECODE Memory Bank.*?(?=##|\z)', ''
    $Content = $Content -replace '(?ms)### \*\*🚀 SMART MEMORY BANK.*?(?=###|\z)', ''
    
    # Adiciona nova integração no final
    $NewIntegration = Get-IntegrationTemplate
    $UpdatedContent = $Content.TrimEnd() + "`n`n" + $NewIntegration
    
    if (-not $DryRun) {
        $UpdatedContent | Out-File -FilePath $FilePath -Encoding UTF8
        Write-Host "🔄 Atualizado: $FileName" -ForegroundColor Cyan
    } else {
        Write-Host "📋 [DRY RUN] Atualizaria: $FileName" -ForegroundColor Yellow
    }
}

function Update-AllInstructions {
    Write-Host "📋 Atualizando Instructions..." -ForegroundColor Blue
    
    if (Test-Path $InstructionsPath) {
        $InstructionFiles = Get-ChildItem -Path $InstructionsPath -Filter "*.md" -Recurse
        
        foreach ($File in $InstructionFiles) {
            Update-InstructionFile -FilePath $File.FullName -FileName $File.Name
        }
        
        Write-Host "✅ $($InstructionFiles.Count) instruction files processados" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Diretório de instructions não encontrado: $InstructionsPath" -ForegroundColor Yellow
    }
}

function Update-AllRules {
    Write-Host "📐 Atualizando Rules..." -ForegroundColor Blue
    
    if (Test-Path $RulesPath) {
        $RuleFiles = Get-ChildItem -Path $RulesPath -Filter "*.md" -Recurse
        
        foreach ($File in $RuleFiles) {
            Update-InstructionFile -FilePath $File.FullName -FileName $File.Name
        }
        
        Write-Host "✅ $($RuleFiles.Count) rule files processados" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Diretório de rules não encontrado: $RulesPath" -ForegroundColor Yellow
    }
}

function Update-CopilotInstructions {
    Write-Host "🤖 Atualizando Copilot Instructions..." -ForegroundColor Blue
    
    # Main copilot instructions
    $MainCopilotPath = Join-Path $WorkspaceRoot ".github\copilot-instructions.md"
    if (Test-Path $MainCopilotPath) {
        Update-InstructionFile -FilePath $MainCopilotPath -FileName "copilot-instructions.md"
    }
    
    # NeonPro copilot instructions
    $NeonProCopilotPath = Join-Path $WorkspaceRoot "neonpro\.github\copilot-instructions.md"
    if (Test-Path $NeonProCopilotPath) {
        Update-InstructionFile -FilePath $NeonProCopilotPath -FileName "neonpro\copilot-instructions.md"
    }
    
    Write-Host "✅ Copilot instructions atualizados" -ForegroundColor Green
}

function Validate-Integration {
    Write-Host "🔍 Validando Integração..." -ForegroundColor Blue
    
    $ValidationResults = @()
    
    # Verifica se memory bank existe
    if (Test-Path $MemoryBankPath) {
        $ValidationResults += "✅ Memory Bank directory exists"
        
        # Verifica arquivos core
        $CoreFiles = @("activeContext.md", "productContext.md", "progress.md", "decisionLog.md", "projectBrief.md", "systemPatterns.md", "techContext.md")
        foreach ($File in $CoreFiles) {
            $FilePath = Join-Path $MemoryBankPath $File
            if (Test-Path $FilePath) {
                $ValidationResults += "✅ Core file exists: $File"
            } else {
                $ValidationResults += "❌ Missing core file: $File"
            }
        }
        
        # Verifica comandos UMB
        $UmbPS1 = Join-Path $MemoryBankPath "umb.ps1"
        $UmbJS = Join-Path $MemoryBankPath "umb.js"
        if (Test-Path $UmbPS1) { $ValidationResults += "✅ UMB PowerShell command exists" }
        if (Test-Path $UmbJS) { $ValidationResults += "✅ UMB Node.js command exists" }
        
        # Verifica smart memory
        $SmartMemory = Join-Path $MemoryBankPath "smart-memory.js"
        if (Test-Path $SmartMemory) { $ValidationResults += "✅ Smart memory system exists" }
        
    } else {
        $ValidationResults += "❌ Memory Bank directory missing"
    }
    
    # Exibe resultados
    Write-Host ""
    Write-Host "📊 Resultados da Validação:" -ForegroundColor Cyan
    foreach ($Result in $ValidationResults) {
        if ($Result.StartsWith("✅")) {
            Write-Host $Result -ForegroundColor Green
        } else {
            Write-Host $Result -ForegroundColor Red
        }
    }
}

function Show-Summary {
    Write-Host ""
    Write-Host "🎉 INTEGRAÇÃO CONCLUÍDA!" -ForegroundColor Green
    Write-Host "📋 Resumo das atualizações:" -ForegroundColor Cyan
    Write-Host "   ✅ Memory Bank V5.0 estrutura criada" -ForegroundColor Green
    Write-Host "   ✅ Triggers em português implementados" -ForegroundColor Green
    Write-Host "   ✅ Smart loading com <200ms target" -ForegroundColor Green
    Write-Host "   ✅ Comandos UMB funcionais" -ForegroundColor Green
    Write-Host "   ✅ Integração com todas rules/instructions" -ForegroundColor Green
    Write-Host ""
    Write-Host "💡 Próximos passos:" -ForegroundColor Blue
    Write-Host "   1. Use triggers naturais: 'lembre-se', 'implementar', 'continuar'" -ForegroundColor Gray
    Write-Host "   2. Execute '.\umb.ps1 status' para verificar estado" -ForegroundColor Gray
    Write-Host "   3. Sistema aprende automaticamente com uso" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🚀 VIBECODE Memory Bank V5.0 está ATIVO!" -ForegroundColor Cyan
}

# Main execution
try {
    Write-Header
    
    if ($Validate) {
        Validate-Integration
    } elseif ($UpdateOnly) {
        Update-AllInstructions
        Update-AllRules
        Update-CopilotInstructions
    } else {
        # Full integration
        Update-AllInstructions
        Update-AllRules
        Update-CopilotInstructions
        Validate-Integration
        Show-Summary
    }
}
catch {
    Write-Host "❌ Erro durante integração: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
