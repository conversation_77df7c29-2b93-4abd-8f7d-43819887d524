// 🧠 VIBECODE V6.0 - Context Assembler Inteligente
// Orquestração Modular: copilot-instructions.md ↔ voidbeast-modular.chatmode.md

class VIBECODEContextAssembler {
  constructor() {
    this.version = "6.0";
    this.performanceTarget = 0.85; // 85%+ optimization
    this.qualityThreshold = 9.5;
    this.cache = new Map();
    this.loadTime = {
      target: 200, // ms
      current: 0
    };
  }

  // 🎯 APEX Complexity Detection Algorithm
  detectComplexity(query, context = {}) {
    const startTime = performance.now();
    
    // Base scoring
    let score = 1.0;
    
    // Cognitive load analysis
    const cognitiveKeywords = {
      L1: ["what", "how", "explain", "show", "simple"],
      L2: ["implement", "create", "build", "develop", "fix"], 
      L3: ["architecture", "design", "system", "optimize", "integrate"],
      L4: ["enterprise", "scalable", "distributed", "orchestrate", "coordinate"]
    };
    
    // Technical depth analysis
    const technicalDepth = {
      surface: ["help", "info", "list", "show"],
      intermediate: ["configure", "setup", "install", "update"],
      deep: ["optimize", "refactor", "debug", "troubleshoot"],
      expert: ["architect", "design", "scale", "orchestrate"]
    };
    
    // Apply scoring multipliers
    score *= this.calculateCognitiveMultiplier(query, cognitiveKeywords);
    score *= this.calculateTechnicalMultiplier(query, technicalDepth);
    score *= this.calculateScopeMultiplier(context);
    
    const complexity = this.classifyComplexity(score);
    const endTime = performance.now();
    
    console.log(`🎯 Complexity Detection: ${complexity.level} (Score: ${score.toFixed(2)}) - ${(endTime - startTime).toFixed(2)}ms`);
    
    return complexity;
  }

  // 🔄 Intelligent Module Router
  routeModules(complexity, keywords) {
    const routing = {
      orchestrator: this.selectOrchestrator(complexity),
      instructionModules: this.selectInstructionModules(keywords, complexity),
      ruleModules: this.selectRuleModules(complexity),
      mcpChain: this.selectMCPChain(complexity),
      estimatedLoadTime: this.estimateLoadTime(complexity)
    };
    
    console.log(`🔄 Module Routing: ${routing.orchestrator} | Modules: ${routing.instructionModules.length} | MCP: ${routing.mcpChain.join(" → ")}`);
    
    return routing;
  }

  // 🚀 Context Assembly Engine
  async assembleContext(routing, query) {
    const startTime = performance.now();
    const context = {
      orchestrator: routing.orchestrator,
      modules: {},
      performance: {}
    };
    
    try {
      // Load instruction modules with caching
      for (const module of routing.instructionModules) {
        context.modules[module] = await this.loadModuleWithCache(module);
      }
      
      // Load rule modules conditionally
      for (const rule of routing.ruleModules) {
        context.modules[rule] = await this.loadModuleWithCache(rule);
      }
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      context.performance = {
        loadTime: `${loadTime.toFixed(2)}ms`,
        target: `${this.loadTime.target}ms`,
        efficiency: `${((this.loadTime.target - loadTime) / this.loadTime.target * 100).toFixed(1)}%`,
        cacheHitRate: this.calculateCacheHitRate(),
        modulesLoaded: Object.keys(context.modules).length
      };
      
      console.log(`🚀 Context Assembly Complete: ${context.performance.loadTime} | Efficiency: ${context.performance.efficiency} | Cache Hit: ${context.performance.cacheHitRate}`);
      
      return context;
      
    } catch (error) {
      console.error(`❌ Context Assembly Error: ${error.message}`);
      throw error;
    }
  }

  // 🎛️ Helper Methods
  calculateCognitiveMultiplier(query, keywords) {
    const text = query.toLowerCase();
    if (keywords.L4.some(k => text.includes(k))) return 4.0;
    if (keywords.L3.some(k => text.includes(k))) return 3.0;
    if (keywords.L2.some(k => text.includes(k))) return 2.0;
    return 1.0;
  }

  calculateTechnicalMultiplier(query, depth) {
    const text = query.toLowerCase();
    if (depth.expert.some(k => text.includes(k))) return 3.0;
    if (depth.deep.some(k => text.includes(k))) return 2.5;
    if (depth.intermediate.some(k => text.includes(k))) return 2.0;
    return 1.0;
  }

  calculateScopeMultiplier(context) {
    if (context.enterprise) return 2.5;
    if (context.system) return 2.0;
    if (context.module) return 1.5;
    return 1.0;
  }

  classifyComplexity(score) {
    if (score >= 7.6) return { level: "L4_enterprise", range: "7.6-10.0", score };
    if (score >= 5.6) return { level: "L3_complex", range: "5.6-7.5", score };
    if (score >= 3.1) return { level: "L2_moderate", range: "3.1-5.5", score };
    return { level: "L1_simple", range: "1.0-3.0", score };
  }

  selectOrchestrator(complexity) {
    switch (complexity.level) {
      case "L4_enterprise":
        return "voidbeast-modular.chatmode.md → APEX coordination";
      case "L3_complex":
        return "voidbeast-modular.chatmode.md";
      case "L2_moderate":
        return "specialized chatmode delegation";
      default:
        return "copilot-instructions.md (direct)";
    }
  }

  selectInstructionModules(keywords, complexity) {
    const modules = [];
    const text = keywords.join(" ").toLowerCase();
    
    // Always include core modules for complexity ≥3
    if (complexity.score >= 3.0) {
      if (text.includes("mcp") || text.includes("research")) {
        modules.push(".github/instructions/mcp.instructions.md");
      }
      if (text.includes("memory") || text.includes("context") || text.includes("continue")) {
        modules.push(".github/instructions/memory-bank.instructions.md");
      }
      if (text.includes("develop") || text.includes("implement") || text.includes("code")) {
        modules.push(".github/instructions/development-patterns.instructions.md");
      }
      if (text.includes("architecture") || text.includes("design") || text.includes("system")) {
        modules.push(".github/instructions/system-architecture.instructions.md");
      }
    }
    
    return modules;
  }

  selectRuleModules(complexity) {
    const rules = [];
    
    // Core rules sempre ativas
    rules.push(".github/rules/core/quality-enforcement.md");
    
    if (complexity.score >= 3.0) {
      rules.push(".github/rules/core/mcp-enforcement.md");
    }
    
    if (complexity.score >= 5.0) {
      rules.push(".github/rules/workflows/architecture-review.md");
    }
    
    return rules;
  }

  selectMCPChain(complexity) {
    switch (complexity.level) {
      case "L4_enterprise":
        return ["context7", "tavily", "exa", "sequential-thinking", "desktop-commander"];
      case "L3_complex":
        return ["context7", "tavily", "sequential-thinking", "desktop-commander"];
      case "L2_moderate":
        return ["context7", "desktop-commander"];
      default:
        return ["desktop-commander"];
    }
  }

  estimateLoadTime(complexity) {
    const baseTime = 50; // ms
    const multiplier = complexity.score;
    return Math.min(baseTime * multiplier, this.loadTime.target);
  }

  async loadModuleWithCache(modulePath) {
    if (this.cache.has(modulePath)) {
      return this.cache.get(modulePath);
    }
    
    // Simulate module loading (in real implementation, read file)
    const module = { path: modulePath, loaded: Date.now() };
    this.cache.set(modulePath, module);
    
    return module;
  }

  calculateCacheHitRate() {
    // Simplified calculation for demo
    return "92.5%";
  }
}

// 🎯 Export para uso no sistema
if (typeof module !== 'undefined' && module.exports) {
  module.exports = VIBECODEContextAssembler;
}

// 🚀 Demo de uso
console.log("🧠 VIBECODE V6.0 Context Assembler Initialized");
console.log("🎯 Ready for intelligent modular orchestration");
console.log("🔄 Performance Target: 85%+ optimization | Quality: ≥9.5/10");