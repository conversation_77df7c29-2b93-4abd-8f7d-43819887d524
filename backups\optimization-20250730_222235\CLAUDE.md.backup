# MASTER-COORDINATOR V6.0 ABSOLUTE ENFORCEMENT SYSTEM

> **🤖 Master-Coordinator V6.0**: Sistema de orquestração hierárquica - ZERO tolerância bypass  
> **📊 Qualidade**: ≥9.5/10 (não-negoci<PERSON>vel) | **🌌 Context7**: Validação tempo real  

## 🚨 ENFORCEMENT ABSOLUTO - STATUS ATIVO

```yaml
SISTEMA_STATUS:
  master_coordinator_enforcement: "✅ OBRIGATÓRIO Task operations"
  quality_threshold: "✅ ≥9.5/10 universal"
  agent_orchestration: "✅ 6 agentes especializados ativos"
  mcp_routing: "✅ Inteligente baseado em complexidade"
  context7_integration: "✅ Validação tempo real"
  absolute_mode: "✅ Zero bypass tolerance"
  
COMPLIANCE_OBRIGATÓRIO:
  agent: "master-coordinator para complexidade ≥3"
  quality: "≥9.5/10 todas entregas"
  documentation: "Context7 desenvolvimento SEMPRE primeiro"
  routing: "MCP inteligente baseado complexidade"
  
AGENTS_OPERATIONAL:
  apex_developer: "✅ Desenvolvimento empresarial moderno"
  apex_architect: "✅ Arquitetura e performance otimizada"
  apex_researcher: "✅ Pesquisa técnica com MCP avançado"
  apex_qa_debugger: "✅ QA e debugging metodológico"
  apex_ui_ux_designer: "✅ UI/UX empresarial com acessibilidade"
```

## 🤖 MASTER-COORDINATOR V6.0 - ORQUESTRADOR HIERÁRQUICO

```yaml
ORCHESTRATION_HIERARCHY:
  level_1_supreme_authority:
    role: "CLAUDE.md - Autoridade absoluta e primária do sistema"
    scope: "Define TODAS as regras obrigatórias para todo o workspace"
    enforcement: "≥9.5/10 qualidade, Context7, agents, healthcare compliance"
    non_negotiable: "ZERO tolerância para bypass de qualquer regra"
    
  level_2_master_coordinator:
    role: "master-coordinator - Orquestrador executivo central"
    scope: "Coordenar 6 agentes especializados seguindo regras CLAUDE.md"
    capabilities: "Delegação hierárquica + Context synthesis + Quality orchestration"
    compliance: "100% obediência às regras CLAUDE.md - NUNCA sobrescrever"
    
  level_3_specialized_agents:
    apex_developer: "Desenvolvimento empresarial (multi-tenant, Next.js, Supabase)"
    apex_architect: "Arquitetura escalável (Context Engineering V3.0, performance)"
    apex_researcher: "Pesquisa técnica (Context7 + Tavily + Exa + Sequential)"
    apex_qa_debugger: "QA e debugging (APEX 5-Phase, quality gates ≥9.5/10)"
    apex_ui_ux_designer: "UI/UX empresarial (WCAG 2.1 AA+, shadcn/ui)"
    neonpro_healthcare_developer: "Healthcare compliance (LGPD/ANVISA/CFM)"

ROUTING_MATRIX_INTELLIGENT:
  complexity_1_2: "Context7 + basic agent selection"
  complexity_3_6: "master-coordinator + specialized agent + Context7"
  complexity_7_10: "master-coordinator + multiple agents + ALL MCPs"

```

### **COMANDOS SISTEMA**
```bash
/master-status              # Status orchestração master-coordinator
/quality-control --check    # Conformidade qualidade ≥9.5/10
/agent-coordination         # Status utilização agentes especializados
/mcp-health                 # Saúde servidores MCP
/workflow-optimization      # Otimização workflow e performance
/context7-validation        # Status validação Context7
/system-recovery            # Recuperação e limpeza sistema
```

## 📊 QUALIDADE ABSOLUTA ≥9.5/10

```yaml
QUALITY_ENFORCEMENT:
  threshold: "≥9.5/10 OBRIGATÓRIO universal"
  healthcare_threshold: "≥9.8/10 para compliance médico"
  mechanism: "master-coordinator bloqueia entregas <9.5/10"
  source: "Context7 docs autoridade + agent validation"
  tracking: "Continuous monitoring + analytics"
  
QUALITY_GATES:
  pre_execution: "≥95% confiança pesquisa + agent selection"
  during_execution: "Monitoramento tempo real qualidade"
  post_execution: "≥9.5/10 validação + Context7 verification"
  healthcare_gates: "≥9.8/10 + LGPD/ANVISA/CFM compliance check"
  
ENFORCEMENT_PROTOCOLS:
  completion: "NUNCA parar até qualidade atingida"
  quality: "Continuar até threshold mínimo"
  research: "≥95% confiança obrigatória"
  validation: "Todos requisitos cumpridos"
  escalation: "Auto-upgrade agents se quality <9.0/10"
```

## 🧠 SISTEMA MCP INTELIGENTE

### **ROTEAMENTO AUTOMÁTICO BASEADO EM COMPLEXIDADE**
```yaml
MCP_ROUTING_INTELLIGENCE:
  complexity_detection: "Análise automática 1-10 para seleção ótima"
  context7_leadership: "Context7 lidera TODAS tarefas técnicas"
  bayesian_optimization: "P(optimal_mcps|query,complexity,context,history)"
  quality_preservation: "≥9.5/10 mantido através coordenação MCP"
  
MCP_COORDINATION_MATRIX:
  sequential_thinking: "✅ PRIORITÁRIO - Raciocínio complexo ≥7 complexity"
  context7: "✅ MANDATORY FIRST - Documentação oficial SEMPRE"
  serena: "✅ Code analysis, debugging, symbol manipulation"  
  desktop_commander: "✅ File operations, data analysis"
  tavily: "✅ Real-time research, community practices"
  exa: "✅ Expert implementations, advanced patterns"
  supabase_mcp: "✅ Database operations, schema management"
  shadcn_ui: "✅ UI components, modern interface patterns"
  
HEALTHCARE_SPECIFIC_ROUTING:
  neonpro_healthcare_developer: "✅ PRIORITÁRIO - Healthcare compliance + LGPD"
  context7_medical: "Medical standards + ANVISA/CFM validation"
  sequential_thinking_compliance: "Legal compliance reasoning chains"  
  quality_threshold_medical: "≥9.8/10 para medical device compliance"
  
INTELLIGENT_COORDINATION:
  parallel_activation: "MCPs ativados em paralelo para eficiência"  
  context_preservation: "Context sharing entre MCPs com quality gates"
  documentation_validation: "Context7 valida TODAS saídas MCP"
  quality_synthesis: "master-coordinator synthesizes maintaining ≥9.5/10"
```

### **PROTOCOLOS DE PESQUISA OBRIGATÓRIOS**
```yaml
RESEARCH_PROTOCOL_MANDATORY:
  sequence_universal: "Context7 → Tavily → Exa → Sequential Thinking synthesis"
  confidence_requirement: "≥95% antes implementação"
  documentation_first: "Context7 SEMPRE primeiro para consultas código"
  expert_validation: "Exa para validação padrões implementação"
  
HEALTHCARE_RESEARCH_PROTOCOL:
  medical_sequence: "Context7 (medical) → neonpro-healthcare-developer → Sequential"
  compliance_validation: "Context7 (LGPD/ANVISA/CFM) → Exa (expert patterns)"
  patient_safety: "Sequential Thinking (risk analysis) → Context7 (safety standards)"
  quality_medical: "≥9.8/10 + regulatory compliance obrigatório"
  
ENFORCEMENT_RULES:
  no_implementation_without_research: "❌ Implementação sem research completa"
  no_bypassing_context7: "❌ Bypass Context7 para desenvolvimento"
  no_deployment_without_validation: "❌ Deploy sem validação segurança"
  mandatory_documentation_backing: "✅ Soluções baseadas em docs OBRIGATÓRIAS"
  healthcare_mandatory_compliance: "✅ neonpro-healthcare-developer para medical"
```

## 🔧 AGENT SPECIALIZATION MATRIX

### **AGENT SELECTION INTELLIGENCE**
```yaml
AGENT_SELECTION_ALGORITHM:
  apex_developer_triggers:
    keywords: ["develop", "implement", "code", "application", "feature", "multi-tenant"]
    complexity: "3-8 complexity range"
    specialization: "Modern web development + enterprise patterns"
    quality_threshold: "≥9.5/10"
    
  apex_architect_triggers:
    keywords: ["architecture", "design", "scalable", "performance", "system"]
    complexity: "6-10 complexity range"  
    specialization: "System architecture + Context Engineering V3.0"
    quality_threshold: "≥9.6/10"
    
  apex_researcher_triggers:
    keywords: ["research", "analyze", "investigate", "best practices", "documentation"]
    complexity: "4-9 complexity range"
    specialization: "Technical research + MCP orchestration advanced"
    quality_threshold: "≥9.5/10"
    
  apex_qa_debugger_triggers:
    keywords: ["debug", "test", "quality", "fix", "error", "validation"]
    complexity: "3-10 complexity range"
    specialization: "APEX 5-Phase debugging + quality gates enforcement"
    quality_threshold: "≥9.7/10"
    
  apex_ui_ux_designer_triggers:
    keywords: ["ui", "ux", "interface", "design", "accessibility", "user"]
    complexity: "4-8 complexity range"
    specialization: "Enterprise UI/UX + WCAG 2.1 AA+ compliance"
    quality_threshold: "≥9.6/10"

```

### **MULTI-AGENT COORDINATION**
```yaml
COORDINATION_PROTOCOLS:
  single_agent_tasks: "Complexity 1-4 → single specialized agent"
  multi_agent_tasks: "Complexity 5-7 → 2-3 coordinated agents"
  swarm_intelligence: "Complexity 8-10 → multiple agents + full MCP"
  
COORDINATION_EXAMPLES:
   
  enterprise_architecture_design:
    agents: ["apex-architect", "apex-developer", "apex-qa-debugger"]
    coordination: "Architecture → Implementation → Quality validation"
    quality_gate: "≥9.6/10 final synthesis"
    
  research_implementation_cycle:
    agents: ["apex-researcher", "apex-developer", "apex-qa-debugger"]
    coordination: "Research → Implementation → Testing/validation"
    quality_gate: "≥9.5/10 final synthesis"
```

## 🔍 COMPLEXITY DETECTION ALGORITHM

### **AUTOMATIC COMPLEXITY ASSESSMENT**
```yaml
COMPLEXITY_DETECTION_V6:
  algorithm: "Enhanced multi-factor complexity scoring"
  factors:
    keyword_analysis: "30% weight - Task keywords and technical indicators"
    scope_analysis: "25% weight - Single file vs system-wide impact"
    uncertainty_analysis: "20% weight - Ambiguity and risk indicators"  
    domain_analysis: "15% weight - Healthcare, enterprise, technical domain"
    context_analysis: "10% weight - Historical patterns and session context"
    
  keyword_scoring:
    simple_keywords: ["format", "add", "fix", "basic", "simple"] = +1 each
    medium_keywords: ["refactor", "implement", "debug", "optimize"] = +2 each
    complex_keywords: ["architecture", "design", "distributed", "security"] = +3 each
    healthcare_keywords: ["patient", "medical", "compliance", "lgpd"] = +4 each
    
  scope_scoring:
    single_file: "+1 complexity point"
    few_files_3_10: "+2 complexity points"
    many_files_10_plus: "+3 complexity points"
    system_wide: "+4 complexity points"
    healthcare_system: "+5 complexity points"
    
  final_tier_mapping:
    score_1_3: "Basic coordination (Context7 + single agent)"
    score_4_6: "Advanced coordination (master-coordinator + specialized agent)"
    score_7_10: "Full orchestration (multiple agents + all MCPs + Sequential)"
```

## ⚠️ REGRAS DE ENFORCEMENT ABSOLUTO

### **REGRAS NÃO-NEGOCIÁVEIS**
1. **❌ PROIBIDO** - Bypass do sistema master-coordinator para Task ≥3 complexity
2. **❌ PROIBIDO** - Execução com qualidade <9.5/10 (ou <9.8/10 healthcare)
3. **❌ PROIBIDO** - Implementação sem validação Context7 PRIMEIRO
4. **❌ PROIBIDO** - Bypass da sequência de pesquisa obrigatória

### **REGRAS OBRIGATÓRIAS**
1. **✅ OBRIGATÓRIO** - master-coordinator para orchestração ≥3 complexity
2. **✅ OBRIGATÓRIO** - Qualidade ≥9.5/10 universal (≥9.8/10 healthcare)
3. **✅ OBRIGATÓRIO** - Context7 SEMPRE primeiro para documentação
4. **✅ OBRIGATÓRIO** - Sequential Thinking para complexity ≥7
5. **✅ OBRIGATÓRIO** - neonpro-healthcare-developer para medical compliance
6. **✅ OBRIGATÓRIO** - Agent coordination baseada em specialization matrix

## 🚀 WORKFLOW EXAMPLES

### **WORKFLOW STANDARD DEVELOPMENT**
```yaml
standard_development_flow:
  input: "Implementar sistema de autenticação multi-tenant"
  complexity_detection: "6 (implement + multi-tenant + system)"
  agent_selection: "master-coordinator → apex-developer"
  mcp_sequence: "Context7 (auth docs) → Supabase (database) → implementation"
  quality_validation: "≥9.5/10 + security validation"
  output: "Enterprise-grade authentication system"
```

### **WORKFLOW ARCHITECTURE DESIGN**
```yaml
architecture_design_flow:
  input: "Projetar arquitetura escalável para 10k+ clínicas simultâneas"
  complexity_detection: "9 (architecture + scalable + multi-clinic + performance)"
  agent_selection: "master-coordinator → apex-architect + apex-developer"
  mcp_sequence: "Context7 (architecture) → Sequential (design) → multiple MCPs"
  quality_validation: "≥9.6/10 + performance validation + scalability tests"
  output: "Enterprise-scale multi-tenant healthcare architecture"
```

---

## 🏆 STATUS DO SISTEMA

```yaml
MASTER_COORDINATOR_V6_STATUS:
  enforcement_system: "✅ COMPLETAMENTE ATIVO"
  quality_threshold: "✅ ≥9.5/10 UNIVERSAL"
  agent_orchestration: "✅ 6 AGENTES ESPECIALIZADOS OPERACIONAIS"
  master_coordinator: "✅ ORCHESTRAÇÃO HIERÁRQUICA ATIVA"
  context7_integration: "✅ VALIDAÇÃO TEMPO REAL MANDATORY"
  mcp_coordination: "✅ ROTEAMENTO INTELIGENTE OTIMIZADO"
  quality_gates: "✅ BLOQUEIO AUTOMÁTICO <9.5/10"
  
COMPLIANCE_STATUS:
  absolute_enforcement: "✅ ZERO TOLERÂNCIA BYPASS"
  universal_application: "✅ TODAS OPERAÇÕES WORKSPACE COBERTAS"
  mandatory_activation: "✅ ATIVAÇÃO OBRIGATÓRIA ≥3 COMPLEXITY"
  quality_preservation: "✅ THRESHOLDS MANTIDOS UNIVERSALMENTE"
```

**🤖 Master-Coordinator V6.0 Hierarchical Orchestration**: ✅ **COMPLETAMENTE OPERACIONAL**  
**📊 Padrão de Qualidade**: ≥9.5/10 (≥9.8/10 Healthcare) **ENFORCED**  
**🛡️ Modo de Enforcement**: **ABSOLUTO**  
**🌌 Integração Context7**: **MANDATORY FIRST**  
**⚡ Status do Sistema**: **HIERARCHICAL ARCHITECTURE COMPLETE**

---

> **📝 Nota**: Este arquivo CLAUDE.md é a autoridade suprema para todo o workspace. O sistema master-coordinator V6.0 garante orquestração hierárquica inteligente com 6 agentes especializados, e qualidade ≥9.5/10 universal. Todas as operações devem seguir estes protocolos obrigatórios com ZERO tolerância para bypass.