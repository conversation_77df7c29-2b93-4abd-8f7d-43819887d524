# Snapshot file
# Unset all aliases to avoid conflicts with functions
unalias -a 2>/dev/null || true
shopt -s expand_aliases
# Check for rg availability
if ! command -v rg >/dev/null 2>&1; then
  alias rg=''\''C:\Users\<USER>\AppData\Roaming\npm\node_modules\@anthropic-ai\claude-code\vendor\ripgrep\x64-win32\rg.exe'\'''
fi
export PATH='/mingw64/bin:/usr/bin:/c/Users/<USER>/bin:/c/Program Files/PowerShell/7:/c/Python313/Scripts:/c/Python313:/c/Windows/system32:/c/Windows:/c/Windows/System32/Wbem:/c/Windows/System32/WindowsPowerShell/v1.0:/c/Windows/System32/OpenSSH:/c/Program Files/dotnet:/e/cursor/resources/app/bin:/c/ProgramData/chocolatey/bin:/c/Program Files/nodejs:/c/Program Files/Docker/Docker/resources/bin:/c/Program Files/PowerShell/7:/cmd:/c/Program Files/starship/bin:/c/Users/<USER>/.cargo/bin:/c/Users/<USER>/AppData/Local/Programs/Trae/bin:/c/Users/<USER>/.local/bin:/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/e/cursor/resources/app/bin:/c/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin:/c/Users/<USER>/AppData/Local/GitHubDesktop/bin:/e/Kiro/bin:/c/Users/<USER>/AppData/Roaming/npm:/c/Users/<USER>/.bun/bin'
