/**
 * VIBECODE Smart Memory System V5.0
 * Ultra-Simplified Memory Bank following roo-code-memory-bank patterns
 * Enhanced with Portuguese triggers and intelligent activation
 */

class VIBECODESmartMemory {
  constructor() {
    this.coreFiles = [
      "activeContext.md", // Contexto atual (roo-code padrão)
      "productContext.md", // Visão do produto (roo-code padrão)
      "progress.md", // Progresso (roo-code padrão)
      "decisionLog.md", // Decisões (roo-code padrão)
      "projectBrief.md", // Overview (roo-code padrão)
      "systemPatterns.md", // Padrões do sistema (VIBECODE extensão)
      "techContext.md", // Contexto técnico (VIBECODE extensão)
    ];

    this.cache = new Map();
    this.loadingStrategy = "smart";
    this.performanceTarget = 200; // ms
    this.sessionStats = {
      loads: 0,
      hits: 0,
      averageLoadTime: 0,
    };
  }

  /**
   * TRIGGERS INTELIGENTES - PORTUGUÊS + INGLÊS EXPANDIDO
   * Auto-detecção baseada em keywords que indicam necessidade de contexto
   */
  getActivationTriggers() {
    return {
      // Triggers de alta prioridade (sempre ativar memory bank)
      high_priority: [
        // Português - Continuação e implementação
        "continue",
        "continuar",
        "prosseguir",
        "seguir",
        "próxima",
        "próximo",
        "implementar",
        "implementação",
        "desenvolver",
        "desenvolvimento",
        "criar",
        "construir",
        "build",
        "develop",
        "implement",
        "create",

        // Português - Contexto e memória
        "lembre-se",
        "lembre",
        "não se esqueça",
        "não esqueça",
        "relembre",
        "contexto",
        "histórico",
        "decisões",
        "padrões",
        "remember",
        "context",

        // Português - Projetos específicos
        "neonpro",
        "clinic",
        "clínica",
        "médico",
        "saúde",
        "healthcare",

        // Português - Debug e otimização
        "debug",
        "debugar",
        "corrigir",
        "fix",
        "otimizar",
        "melhorar",
        "optimize",
        "problema",
        "erro",
        "issue",
        "bug",
        "trouble",

        // Português - Documentação e padrões
        "documentar",
        "padrão",
        "arquitetura",
        "design",
        "estrutura",
      ],

      // Triggers de média prioridade (ativar condicionalmente)
      medium_priority: [
        "revisar",
        "review",
        "validar",
        "testar",
        "test",
        "analisar",
        "analyze",
        "refatorar",
        "refactor",
        "melhorar",
        "improve",
        "enhance",
      ],

      // Palavras que indicam queries simples (pular memory bank)
      skip_triggers: [
        "o que",
        "what",
        "como",
        "how",
        "porque",
        "why",
        "quando",
        "when",
        "onde",
        "where",
        "explique",
        "explain",
        "defina",
        "define",
        "mostre",
        "show",
        "liste",
        "list",
        "help",
        "ajuda",
      ],
    };
  }

  /**
   * Detecção inteligente de necessidade de memory bank
   * @param {string} query - Query do usuário
   * @param {number} complexity - Score de complexidade (1-10)
   * @returns {boolean} - Se deve ativar memory bank
   */
  async shouldActivate(query, complexity = 5) {
    const triggers = this.getActivationTriggers();
    const queryLower = query.toLowerCase();

    // Skip para queries claramente simples
    if (triggers.skip_triggers.some((word) => queryLower.includes(word))) {
      // Exceto se for uma query complexa mesmo com skip words
      if (complexity < 7 && query.length < 100) {
        return false;
      }
    }

    // High priority triggers sempre ativam
    if (triggers.high_priority.some((word) => queryLower.includes(word))) {
      return true;
    }

    // Medium priority com complexidade
    if (
      triggers.medium_priority.some((word) => queryLower.includes(word)) &&
      complexity >= 4
    ) {
      return true;
    }

    // Ativação baseada em complexidade
    return complexity >= 6;
  }

  /**
   * Carregamento otimizado de contexto relevante
   * @param {Object} taskContext - Contexto da task atual
   * @returns {Promise<Object>} - Contexto carregado
   */
  async loadRelevantContext(taskContext = {}) {
    const startTime = Date.now();
    const loadedContent = {};

    try {
      // Nível 1: Sempre carrega activeContext (essencial)
      loadedContent.activeContext = await this.loadFile("activeContext.md");

      // Nível 2: Carregamento condicional baseado em contexto
      if (taskContext.type === "continuation" || taskContext.hasHistory) {
        loadedContent.progress = await this.loadFile("progress.md");
        loadedContent.decisions = await this.loadFile("decisionLog.md");
      }

      // Nível 3: Contexto técnico para implementação
      if (
        taskContext.type === "implementation" ||
        taskContext.complexity >= 7
      ) {
        loadedContent.systemPatterns = await this.loadFile("systemPatterns.md");
        loadedContent.techContext = await this.loadFile("techContext.md");
      }

      // Nível 4: Contexto completo para arquitetura
      if (taskContext.type === "architecture" || taskContext.complexity >= 8) {
        loadedContent.productContext = await this.loadFile("productContext.md");
        loadedContent.projectBrief = await this.loadFile("projectBrief.md");
      }

      const loadTime = Date.now() - startTime;
      this.updatePerformanceStats(loadTime);

      // Auto-optimization se performance degradar
      if (loadTime > this.performanceTarget) {
        await this.optimizeLoadingStrategy();
      }

      return {
        content: loadedContent,
        loadTime,
        filesLoaded: Object.keys(loadedContent).length,
        performance: this.getPerformanceStatus(),
      };
    } catch (error) {
      console.warn("Memory Bank load error:", error.message);
      return {
        content: {},
        loadTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  /**
   * Carrega arquivo individual com cache inteligente
   */
  async loadFile(filename) {
    const cacheKey = `${filename}_${Date.now() - (Date.now() % 300000)}`; // 5min cache

    if (this.cache.has(cacheKey)) {
      this.sessionStats.hits++;
      return this.cache.get(cacheKey);
    }

    try {
      const fs = require("fs").promises;
      const path = require("path");
      const filePath = path.join(process.cwd(), "memory-bank", filename);

      const content = await fs.readFile(filePath, "utf8");
      this.cache.set(cacheKey, content);
      this.sessionStats.loads++;

      return content;
    } catch (error) {
      console.warn(`Could not load ${filename}:`, error.message);
      return "";
    }
  }

  /**
   * Auto-update inteligente do memory bank
   * @param {Object} taskResult - Resultado da task executada
   */
  async autoUpdate(taskResult) {
    if (!taskResult.shouldPersist) return;

    const updates = [];

    // Update activeContext se contexto mudou
    if (taskResult.contextChanged) {
      updates.push(this.updateActiveContext(taskResult));
    }

    // Update progress se milestone atingido
    if (taskResult.progressMade) {
      updates.push(this.updateProgress(taskResult));
    }

    // Update decisionLog se decisão arquitetural tomada
    if (taskResult.architecturalDecision) {
      updates.push(this.updateDecisionLog(taskResult));
    }

    // Update systemPatterns se novo padrão descoberto
    if (taskResult.patternLearned) {
      updates.push(this.updateSystemPatterns(taskResult));
    }

    await Promise.all(updates);
    await this.learnFromInteraction(taskResult);
  }

  /**
   * Sistema de aprendizado para otimização contínua
   */
  async learnFromInteraction(taskResult) {
    // Aprende padrões de uso
    const usagePattern = {
      timestamp: Date.now(),
      taskType: taskResult.type,
      complexity: taskResult.complexity,
      filesUsed: taskResult.filesAccessed,
      qualityScore: taskResult.qualityScore,
      loadTime: taskResult.loadTime,
    };

    // Salva padrões para otimização futura
    await this.saveUsagePattern(usagePattern);

    // Auto-ajuste baseado em performance
    if (
      taskResult.qualityScore >= 9.5 &&
      taskResult.loadTime < this.performanceTarget
    ) {
      // Padrão bem-sucedido - reforçar estratégia
      await this.reinforceStrategy(usagePattern);
    }
  }

  /**
   * Comando ultra-simples equivalente ao "UMB" do roo-code
   */
  async executeUMBCommand(context = {}) {
    console.log("🧠 VIBECODE Memory Bank - Auto Update...");

    try {
      // Analisa contexto atual
      const currentContext = await this.analyzeCurrentContext();

      // Update arquivos relevantes
      await this.autoUpdate({
        shouldPersist: true,
        contextChanged: currentContext.hasChanges,
        progressMade: currentContext.hasProgress,
        architecturalDecision: currentContext.hasDecisions,
        patternLearned: currentContext.hasPatterns,
        type: currentContext.inferredType,
        complexity: currentContext.complexity,
        qualityScore: 9.5, // Assume high quality para UMB manual
      });

      console.log("✅ Memory Bank atualizado com sucesso!");
      console.log(`📊 Performance: ${this.getPerformanceStatus()}`);
    } catch (error) {
      console.error("❌ Erro ao atualizar Memory Bank:", error.message);
    }
  }

  /**
   * Status de performance e otimização
   */
  getPerformanceStatus() {
    const cacheHitRate =
      (this.sessionStats.hits /
        (this.sessionStats.loads + this.sessionStats.hits)) *
      100;
    return {
      averageLoadTime: `${this.sessionStats.averageLoadTime}ms`,
      cacheHitRate: `${cacheHitRate.toFixed(1)}%`,
      totalLoads: this.sessionStats.loads,
      status:
        this.sessionStats.averageLoadTime < this.performanceTarget
          ? "🟢 Optimal"
          : "🟡 Optimizing",
    };
  }

  /**
   * Otimização automática da estratégia de carregamento
   */
  async optimizeLoadingStrategy() {
    console.log("🔧 Auto-optimizing memory loading strategy...");

    // Análise de padrões de uso
    const usageData = await this.getUsagePatterns();

    // Ajusta thresholds baseado em performance histórica
    if (usageData.averageLoadTime > this.performanceTarget) {
      this.performanceTarget = Math.min(this.performanceTarget * 1.2, 500);
      console.log(
        `⚡ Performance target adjusted to ${this.performanceTarget}ms`
      );
    }

    // Otimiza cache
    this.optimizeCache();
  }

  /**
   * Atualiza estatísticas de performance
   */
  updatePerformanceStats(loadTime) {
    const total = this.sessionStats.loads + this.sessionStats.hits;
    this.sessionStats.averageLoadTime =
      (this.sessionStats.averageLoadTime * total + loadTime) / (total + 1);
  }

  /**
   * Limpa cache otimamente
   */
  optimizeCache() {
    if (this.cache.size > 20) {
      // Remove entradas mais antigas
      const entries = Array.from(this.cache.entries());
      entries.slice(0, 10).forEach(([key]) => this.cache.delete(key));
    }
  }
}

// Export para uso global
if (typeof module !== "undefined" && module.exports) {
  module.exports = VIBECODESmartMemory;
}

// Instância global para uso direto
if (typeof window !== "undefined") {
  window.VIBECODESmartMemory = VIBECODESmartMemory;
}

console.log("🧠 VIBECODE Smart Memory System V5.0 carregado!");
console.log(
  "📝 Triggers em Português: lembre-se, não se esqueça, implementar, continuar, etc."
);
console.log("⚡ Performance Target: Sub-200ms | Auto-optimization: Ativa");
