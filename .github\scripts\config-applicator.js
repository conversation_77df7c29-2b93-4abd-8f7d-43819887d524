#!/usr/bin/env node

/**
 * 🔧 VIBECODE V6.0 - Aplicador Final de Configurações
 * Resolve L1 Configuration com aplicação automática e validação completa
 */

const fs = require('fs').promises;
const path = require('path');

class ConfigurationApplicator {
    constructor() {
        this.basePath = process.cwd();
        this.vscodeSettingsPath = path.join(this.basePath, '.vscode', 'settings.json');
        this.orchestrationSettingsPath = path.join(this.basePath, '.github', 'config', 'orchestration-settings.json');
        this.copilotSettingsPath = path.join(this.basePath, '.github', 'config', 'copilot-settings.json');
    }

    /**
     * 🚀 Aplicação Completa de Configurações
     */
    async applyAllConfigurations() {
        console.log('🚀 APLICAÇÃO FINAL DE CONFIGURAÇÕES - VIBECODE V6.0\n');
        console.log('='.repeat(80));

        const results = [];

        try {
            // 1. Carregar configurações
            console.log('📋 CARREGANDO CONFIGURAÇÕES...');
            const orchestrationConfig = await this.loadJSONFile(this.orchestrationSettingsPath);
            const copilotConfig = await this.loadJSONFile(this.copilotSettingsPath);
            
            // 2. Aplicar configurações VS Code
            console.log('\n🔧 APLICANDO CONFIGURAÇÕES VS CODE...');
            const vscodeResult = await this.applyVSCodeSettings(orchestrationConfig, copilotConfig);
            results.push(vscodeResult);

            // 3. Validar arquivos de instrução
            console.log('\n📁 VALIDANDO ARQUIVOS DE INSTRUÇÃO...');
            const instructionResult = await this.validateInstructionFiles();
            results.push(instructionResult);

            // 4. Validar chatmodes
            console.log('\n🤖 VALIDANDO CHATMODES...');
            const chatmodeResult = await this.validateChatmodes();
            results.push(chatmodeResult);

            // 5. Teste de integração
            console.log('\n🧪 TESTE DE INTEGRAÇÃO FINAL...');
            const integrationResult = await this.testIntegration();
            results.push(integrationResult);

            // 6. Relatório final
            console.log('\n📊 RELATÓRIO FINAL...');
            this.generateFinalReport(results);

            return results.every(r => r.success);

        } catch (error) {
            console.error('❌ ERRO NA APLICAÇÃO:', error.message);
            return false;
        }
    }

    /**
     * 📋 Carregar arquivo JSON
     */
    async loadJSONFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            return JSON.parse(content);
        } catch (error) {
            console.warn(`⚠️  Arquivo não encontrado: ${filePath}`);
            return {};
        }
    }

    /**
     * 🔧 Aplicar configurações no VS Code
     */
    async applyVSCodeSettings(orchestrationConfig, copilotConfig) {
        try {
            // Carregar settings.json atual ou criar vazio
            let currentSettings = {};
            try {
                const content = await fs.readFile(this.vscodeSettingsPath, 'utf8');
                currentSettings = JSON.parse(content);
            } catch {
                console.log('📝 Criando novo settings.json...');
            }

            // Mesclar configurações
            const mergedSettings = {
                ...currentSettings,
                ...orchestrationConfig.vsCodeSettings,
                ...copilotConfig.vsCodeSettings
            };

            // Configurações essenciais forçadas
            const essentialSettings = {
                "chat.agent.enabled": true,
                "chat.mcp.enabled": true,
                "github.copilot.enable": {
                    "*": true,
                    "yaml": true,
                    "plaintext": true,
                    "markdown": true
                },
                "github.copilot.advanced": {},
                "github.copilot.chat.codeGeneration.useInstructionFiles": true,
                "chat.instructionsFilesLocations": [
                    ".github/instructions/mcp.instructions.md",
                    ".github/instructions/memory-bank.instructions.md",
                    ".github/instructions/performance-optimization.instructions.md",
                    ".github/copilot-instructions.md"
                ],
                "chat.modeFilesLocations": [
                    ".github/chatmodes/"
                ],
                "workbench.colorTheme": "Default Dark+",
                "workbench.editor.showTabs": "single",
                "editor.minimap.enabled": false,
                "editor.fontSize": 14,
                "editor.fontFamily": "Consolas, 'Courier New', monospace",
                "terminal.integrated.defaultProfile.windows": "PowerShell",
                "files.autoSave": "afterDelay",
                "files.autoSaveDelay": 1000
            };

            // Aplicar configurações essenciais
            Object.assign(mergedSettings, essentialSettings);

            // Salvar settings.json
            await this.ensureDirectory(path.dirname(this.vscodeSettingsPath));
            await fs.writeFile(this.vscodeSettingsPath, JSON.stringify(mergedSettings, null, 2));

            console.log('✅ VS Code settings.json aplicado com sucesso');
            console.log(`📍 Localização: ${this.vscodeSettingsPath}`);

            return { success: true, type: 'VSCode Settings', details: 'Settings.json configurado' };

        } catch (error) {
            console.error('❌ Erro ao aplicar VS Code settings:', error.message);
            return { success: false, type: 'VSCode Settings', error: error.message };
        }
    }

    /**
     * 📁 Validar arquivos de instrução
     */
    async validateInstructionFiles() {
        const requiredFiles = [
            '.github/copilot-instructions.md',
            '.github/instructions/mcp.instructions.md',
            '.github/instructions/memory-bank.instructions.md',
            '.github/instructions/performance-optimization.instructions.md'
        ];

        const results = [];
        for (const file of requiredFiles) {
            const fullPath = path.join(this.basePath, file);
            try {
                await fs.access(fullPath);
                results.push({ file, status: '✅ OK' });
            } catch {
                results.push({ file, status: '❌ MISSING' });
            }
        }

        const allExist = results.every(r => r.status.includes('✅'));
        console.log('📁 Arquivos de instrução:');
        results.forEach(r => console.log(`  ${r.file}: ${r.status}`));

        return { 
            success: allExist, 
            type: 'Instruction Files', 
            details: `${results.filter(r => r.status.includes('✅')).length}/${results.length} arquivos encontrados` 
        };
    }

    /**
     * 🤖 Validar chatmodes
     */
    async validateChatmodes() {
        const chatmodeDir = path.join(this.basePath, '.github', 'chatmodes');
        
        try {
            const files = await fs.readdir(chatmodeDir);
            const chatmodeFiles = files.filter(f => f.endsWith('.chatmode.md'));
            
            console.log('🤖 Chatmodes encontrados:');
            chatmodeFiles.forEach(f => console.log(`  ✅ ${f}`));

            return { 
                success: chatmodeFiles.length > 0, 
                type: 'Chatmodes', 
                details: `${chatmodeFiles.length} chatmodes encontrados` 
            };

        } catch (error) {
            console.log('⚠️  Diretório de chatmodes não encontrado');
            return { success: false, type: 'Chatmodes', error: 'Diretório não encontrado' };
        }
    }

    /**
     * 🧪 Teste de integração
     */
    async testIntegration() {
        console.log('🧪 Executando testes de integração...');

        const tests = [
            {
                name: 'Settings.json válido',
                test: async () => {
                    const content = await fs.readFile(this.vscodeSettingsPath, 'utf8');
                    JSON.parse(content); // Verifica se é JSON válido
                    return true;
                }
            },
            {
                name: 'Configurações MCP habilitadas',
                test: async () => {
                    const content = await fs.readFile(this.vscodeSettingsPath, 'utf8');
                    const settings = JSON.parse(content);
                    return settings['chat.mcp.enabled'] === true;
                }
            },
            {
                name: 'GitHub Copilot habilitado',
                test: async () => {
                    const content = await fs.readFile(this.vscodeSettingsPath, 'utf8');
                    const settings = JSON.parse(content);
                    return settings['github.copilot.enable'] && settings['github.copilot.enable']['*'] === true;
                }
            },
            {
                name: 'Instruction files configurados',
                test: async () => {
                    const content = await fs.readFile(this.vscodeSettingsPath, 'utf8');
                    const settings = JSON.parse(content);
                    return Array.isArray(settings['chat.instructionsFilesLocations']) && 
                           settings['chat.instructionsFilesLocations'].length > 0;
                }
            }
        ];

        const results = [];
        for (const test of tests) {
            try {
                const success = await test.test();
                results.push({ name: test.name, success, status: success ? '✅ PASS' : '❌ FAIL' });
                console.log(`  ${test.name}: ${success ? '✅ PASS' : '❌ FAIL'}`);
            } catch (error) {
                results.push({ name: test.name, success: false, status: '❌ ERROR', error: error.message });
                console.log(`  ${test.name}: ❌ ERROR - ${error.message}`);
            }
        }

        const allPassed = results.every(r => r.success);
        return { 
            success: allPassed, 
            type: 'Integration Tests', 
            details: `${results.filter(r => r.success).length}/${results.length} testes passaram` 
        };
    }

    /**
     * 📊 Gerar relatório final
     */
    generateFinalReport(results) {
        console.log('\n📊 RELATÓRIO FINAL DE CONFIGURAÇÃO');
        console.log('='.repeat(50));

        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;
        const successRate = (successCount / totalCount) * 100;

        results.forEach(result => {
            const status = result.success ? '✅ SUCESSO' : '❌ FALHA';
            console.log(`${result.type}: ${status}`);
            if (result.details) console.log(`  ${result.details}`);
            if (result.error) console.log(`  Erro: ${result.error}`);
        });

        console.log('\n' + '='.repeat(50));
        console.log(`🎯 TAXA DE SUCESSO: ${successRate.toFixed(1)}% (${successCount}/${totalCount})`);

        if (successRate >= 80) {
            console.log('🌟 CONFIGURAÇÃO APLICADA COM SUCESSO!');
            console.log('✅ L1 Configuration RESOLVIDO');
            console.log('\n📋 PRÓXIMOS PASSOS:');
            console.log('1. Reinicie o VS Code (Ctrl+Shift+P → "Developer: Reload Window")');
            console.log('2. Verifique se o GitHub Copilot está ativo');
            console.log('3. Teste comandos MCP nos chats');
            console.log('4. Execute: node .github\\scripts\\orchestration-tester.js');
        } else {
            console.log('⚠️  ALGUMAS CONFIGURAÇÕES FALHARAM');
            console.log('📋 AÇÕES NECESSÁRIAS:');
            console.log('1. Verifique os erros acima');
            console.log('2. Execute novamente este script');
            console.log('3. Verifique permissões de arquivo');
        }
    }

    /**
     * 📁 Garantir que diretório existe
     */
    async ensureDirectory(dirPath) {
        try {
            await fs.access(dirPath);
        } catch {
            await fs.mkdir(dirPath, { recursive: true });
        }
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    const applicator = new ConfigurationApplicator();
    applicator.applyAllConfigurations().then(success => {
        console.log(`\n🏁 Aplicação ${success ? 'CONCLUÍDA' : 'FALHOU'}`);
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('💥 ERRO FATAL:', error);
        process.exit(1);
    });
}

module.exports = ConfigurationApplicator;