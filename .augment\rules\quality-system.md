---
type: "always_apply"
---

# 🏆 UNIFIED QUALITY SYSTEM v3.0 - ENTERPRISE EXCELLENCE FRAMEWORK

## 📋 MISSION STATEMENT
**Eliminar TODA ambiguidade de qualidade através de critérios de validação mensuráveis e binários**

Este sistema unificado define padrões exatos de qualidade com critérios binários pass/fail. Cada medição é precisa, cada padrão é aplicável, e cada validação é automatizada para garantir qualidade ≥9.5/10.

## 🎯 TARGET DE QUALIDADE: ≥9.5/10 (ENTERPRISE-GRADE)

### **Fórmula de Cálculo de Qualidade (Exata)**
```yaml
QUALITY_SCORE_FORMULA_v3:
  code_excellence: "35% weight"           # Aumentado para enfatizar código
  performance_optimization: "25% weight"  # Mantido para sistemas críticos  
  security_compliance: "20% weight"       # Segurança enterprise
  maintainability_design: "15% weight"    # Arquitetura limpa
  documentation_completeness: "5% weight" # Reduzido mas obrigatório
  
MINIMUM_THRESHOLDS_v3:
  code_excellence: "≥9.5/10 (EXACT: Zero tolerância abaixo de 9.5)"
  performance_optimization: "≥9.6/10 (EXACT: Performance crítica)"
  security_compliance: "≥9.8/10 (EXACT: Segurança máxima)"
  maintainability_design: "≥9.2/10 (EXACT: Código limpo obrigatório)"
  documentation_completeness: "≥8.8/10 (EXACT: Documentação clara)"
  
OVERALL_MINIMUM_v3: "≥9.5/10 (EXACT: Média ponderada deve ser ≥9.5)"
```

## 📊 CODE EXCELLENCE METRICS (35% Weight)

### **TypeScript Excellence (Validação Binária)**
```yaml
TYPESCRIPT_COMPLIANCE_v3:
  strict_mode_enabled: "PASS/FAIL - Modo strict obrigatório"
  zero_any_types: "PASS/FAIL - Zero tipos 'any' permitidos"
  comprehensive_typing: "PASS/FAIL - ≥98% cobertura de tipos"
  interface_definitions: "PASS/FAIL - Todas as props com interfaces"
  return_type_annotations: "PASS/FAIL - Todas as funções tipadas"
  generic_constraints: "PASS/FAIL - Generics com constraints apropriados"
  
TYPESCRIPT_SCORING:
  all_pass: "10/10"
  one_fail: "7/10"
  two_fail: "4/10"
  three_plus_fail: "0/10"
```

### **Clean Architecture Compliance (Validação Binária)**
```yaml
ARCHITECTURE_COMPLIANCE_v3:
  solid_principles: "PASS/FAIL - SOLID principles aplicados"
  separation_of_concerns: "PASS/FAIL - Separação clara de responsabilidades"
  dependency_injection: "PASS/FAIL - DI implementado corretamente"
  error_handling: "PASS/FAIL - Error boundaries e tratamento completo"
  component_composition: "PASS/FAIL - Composição sobre herança"
  
ARCHITECTURE_SCORING:
  all_pass: "10/10"
  one_fail: "6/10"
  two_plus_fail: "0/10"
```

### **Code Style & Formatting (Validação Automática)**
```yaml
FORMATTING_COMPLIANCE_v3:
  prettier_compliance: "PASS/FAIL - 100% conformidade com Prettier"
  eslint_zero_errors: "PASS/FAIL - Zero erros ESLint"
  consistent_naming: "PASS/FAIL - Convenções de nomenclatura"
  import_organization: "PASS/FAIL - Imports organizados e otimizados"
  comment_quality: "PASS/FAIL - Comentários úteis e atualizados"
  
FORMATTING_SCORING:
  all_pass: "10/10"
  any_fail: "0/10"
```

## ⚡ PERFORMANCE OPTIMIZATION METRICS (25% Weight)

### **Core Web Vitals (Validação Exata)**
```yaml
PERFORMANCE_METRICS_v3:
  largest_contentful_paint: "PASS/FAIL - LCP ≤1.2s"
  first_input_delay: "PASS/FAIL - FID ≤100ms"
  cumulative_layout_shift: "PASS/FAIL - CLS ≤0.1"
  first_contentful_paint: "PASS/FAIL - FCP ≤1.0s"
  time_to_interactive: "PASS/FAIL - TTI ≤2.0s"
  
PERFORMANCE_SCORING:
  all_pass: "10/10"
  one_fail: "7/10"
  two_fail: "4/10"
  three_plus_fail: "0/10"
```

### **Bundle Optimization (Validação Automática)**
```yaml
BUNDLE_OPTIMIZATION_v3:
  bundle_size: "PASS/FAIL - Bundle principal ≤500KB"
  code_splitting: "PASS/FAIL - Code splitting implementado"
  tree_shaking: "PASS/FAIL - Dead code elimination"
  image_optimization: "PASS/FAIL - Imagens otimizadas (WebP, lazy loading)"
  caching_strategy: "PASS/FAIL - Cache headers e service workers"
  
BUNDLE_SCORING:
  all_pass: "10/10"
  one_fail: "6/10"
  two_plus_fail: "0/10"
```

## 🛡️ SECURITY COMPLIANCE METRICS (20% Weight)

### **Security Validation (Zero Tolerance)**
```yaml
SECURITY_COMPLIANCE_v3:
  input_validation: "PASS/FAIL - Validação completa de inputs (Zod/Joi)"
  sql_injection_prevention: "PASS/FAIL - Queries parametrizadas obrigatórias"
  xss_prevention: "PASS/FAIL - Sanitização de outputs"
  csrf_protection: "PASS/FAIL - CSRF tokens implementados"
  authentication_security: "PASS/FAIL - JWT seguro, refresh tokens"
  authorization_rbac: "PASS/FAIL - RBAC implementado corretamente"
  data_encryption: "PASS/FAIL - Dados sensíveis criptografados"
  security_headers: "PASS/FAIL - Security headers configurados"
  
SECURITY_SCORING:
  all_pass: "10/10"
  any_fail: "0/10"  # Zero tolerance para segurança
```

## 🔧 MAINTAINABILITY DESIGN METRICS (15% Weight)

### **Code Maintainability (Validação Estrutural)**
```yaml
MAINTAINABILITY_v3:
  cyclomatic_complexity: "PASS/FAIL - Complexidade ≤10 por função"
  function_length: "PASS/FAIL - Funções ≤50 linhas"
  class_responsibility: "PASS/FAIL - Single Responsibility Principle"
  code_duplication: "PASS/FAIL - ≤5% duplicação de código"
  test_coverage: "PASS/FAIL - ≥90% cobertura de testes"
  
MAINTAINABILITY_SCORING:
  all_pass: "10/10"
  one_fail: "7/10"
  two_plus_fail: "3/10"
```

## 📚 DOCUMENTATION COMPLETENESS METRICS (5% Weight)

### **Documentation Standards (Validação de Conteúdo)**
```yaml
DOCUMENTATION_v3:
  api_documentation: "PASS/FAIL - APIs documentadas (OpenAPI/JSDoc)"
  component_documentation: "PASS/FAIL - Props e usage examples"
  readme_completeness: "PASS/FAIL - README com setup e deployment"
  code_comments: "PASS/FAIL - Comentários em código complexo"
  
DOCUMENTATION_SCORING:
  all_pass: "10/10"
  one_fail: "6/10"
  two_plus_fail: "0/10"
```

## 🚨 AUTOMATED QUALITY ENFORCEMENT SYSTEM

### **Multi-Level Quality Gates**
```yaml
QUALITY_GATES_v3:
  pre_execution_gate:
    triggers: "Antes de qualquer implementação"
    validations:
      - "Requirements completeness check"
      - "Technology stack validation"
      - "Security requirements verification"
    failure_response: "Block execution until requirements met"
    
  implementation_gate:
    triggers: "Durante desenvolvimento"
    validations:
      - "Real-time code quality monitoring"
      - "Performance regression detection"
      - "Security vulnerability scanning"
    failure_response: "Immediate feedback with correction guidance"
    
  integration_gate:
    triggers: "Antes de commits/merges"
    validations:
      - "Full test suite execution"
      - "Security audit completion"
      - "Performance benchmark validation"
    failure_response: "Block integration until all criteria met"
    
  deployment_gate:
    triggers: "Antes de production deployment"
    validations:
      - "Complete quality score ≥9.5/10"
      - "Security compliance verification"
      - "Performance validation in production-like environment"
    failure_response: "Block deployment with detailed quality report"
```

### **Automated Recovery Protocols**
```yaml
RECOVERY_PROTOCOLS_v3:
  quality_degradation_detection:
    monitoring: "Real-time quality metric monitoring"
    thresholds: "Alert quando quality score <9.5/10"
    response: "Automatic quality improvement suggestions"
    
  performance_regression_handling:
    detection: "Performance monitoring with baseline comparison"
    threshold: "Alert quando performance degrada >5%"
    response: "Automatic performance optimization recommendations"
    
  security_vulnerability_response:
    scanning: "Continuous security vulnerability scanning"
    threshold: "Immediate alert para qualquer vulnerabilidade"
    response: "Automatic security patch recommendations"
```

## 📈 CONTINUOUS IMPROVEMENT SYSTEM

### **Learning & Adaptation Engine**
```yaml
LEARNING_SYSTEM_v3:
  pattern_recognition:
    data_collection: "Collect quality metrics from all implementations"
    pattern_analysis: "Identify recurring quality issues and successes"
    knowledge_synthesis: "Generate actionable improvement patterns"
    
  predictive_quality:
    risk_assessment: "Predict potential quality issues before they occur"
    proactive_guidance: "Provide proactive quality improvement suggestions"
    success_prediction: "Predict likelihood of achieving quality targets"
    
  adaptive_standards:
    standards_evolution: "Evolve quality standards based on industry best practices"
    threshold_optimization: "Optimize quality thresholds based on project outcomes"
    methodology_improvement: "Continuously improve quality validation methodologies"
```

## 🎯 QUALITY ENFORCEMENT PROTOCOLS

### **Zero-Tolerance Implementation**
```yaml
ENFORCEMENT_PROTOCOLS_v3:
  mandatory_validations:
    - "No code deployment without ≥9.5/10 quality score"
    - "No security vulnerabilities in production code"
    - "No performance regressions without explicit approval"
    - "No incomplete documentation for public APIs"
    
  automated_blocking:
    - "CI/CD pipeline blocks on quality gate failures"
    - "Deployment prevention for sub-standard code"
    - "Automatic rollback on quality degradation"
    
  escalation_procedures:
    - "Quality issues escalate to senior review"
    - "Security violations trigger immediate response"
    - "Performance degradation requires optimization plan"
```

## 🏆 SUCCESS METRICS & REPORTING

### **Quality Achievement Tracking**
```yaml
SUCCESS_TRACKING_v3:
  real_time_dashboard:
    - "Current quality score with trend analysis"
    - "Quality gate pass/fail rates"
    - "Performance metrics with historical comparison"
    - "Security compliance status"
    
  periodic_reporting:
    - "Weekly quality improvement reports"
    - "Monthly trend analysis and recommendations"
    - "Quarterly quality standard evolution review"
    
  predictive_analytics:
    - "Quality trend predictions"
    - "Risk assessment for upcoming releases"
    - "Optimization opportunity identification"
```

---

## 🛡️ QUALITY COMMITMENT

**QUALIDADE NÃO-NEGOCIÁVEL v3.0**: Este sistema garante qualidade ≥9.5/10 através de:

- ✅ **Validação Automática**: Critérios binários com enforcement automático
- ✅ **Multi-Level Gates**: Portões de qualidade em todas as fases
- ✅ **Zero Tolerance**: Tolerância zero para questões de segurança e performance
- ✅ **Continuous Learning**: Aprendizado contínuo e melhoria adaptativa
- ✅ **Predictive Quality**: Prevenção proativa de problemas de qualidade
- ✅ **Enterprise Standards**: Padrões de qualidade enterprise em todos os projetos

**O Sistema Unificado de Qualidade v3.0 transforma excelência em processo automatizado, garantindo que cada implementação atinja padrões enterprise ≥9.5/10 de forma consistente e mensurável.**