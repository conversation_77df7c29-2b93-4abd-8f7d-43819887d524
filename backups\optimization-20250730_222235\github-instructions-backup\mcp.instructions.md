---
applyTo: "**"
description: 'APEX-Enhanced MCP & Research - Modular Context Injection Compatible'
---

# 🔧 APEX MCP & Research V6.0 - Modular Context Injection

**MODULAR INTEGRATION**: VIBECODE V6.0 Hub-and-Spoke Architecture  
**ACTIVATION**: Trigger-based loading (complexity ≥3.1)  
**ORCHESTRATION**: copilot-instructions.md ↔ voidbeast-modular.chatmode.md  
**RESEARCH BASIS**: Context7 → Tavily → Exa (Mandatory 3-MCP sequence)  
**ENFORCEMENT LEVEL**: APEX ABSOLUTE - Zero tolerance for non-MCP operations  
**QUALITY THRESHOLD**: ≥9.8/10 for all MCP-research operations  

## 🚨 MODULAR MCP & RESEARCH AUTHORITY

### **TRIGGER-ACTIVATED MCP & RESEARCH CONTROL**
```yaml
MODULAR_MCP_RESEARCH_AUTHORITY:
  activation_triggers:
    portuguese: ["mcp", "pesquisar", "investigar", "analisar", "estudar", "comparar"]
    english: ["mcp", "research", "investigate", "analyze", "study", "compare"]
  complexity_threshold: 3.1
  auto_activation: "Smart detection - only when MCP/research value expected"
  orchestrator_coordination: "Seamless handoff to voidbeast-modular.chatmode.md"
  
  mcp_control: "Mandatory MCP usage for ALL file and terminal operations - 100% compliance"
  research_integration: "Mandatory Context7 → Tavily → Exa research for complexity ≥5"
  context_optimization: "85%+ reduction through intelligent MCP selection"
  quality_threshold: "≥9.8/10 for all MCP-research operations"
  
MODULAR_MCP_PRINCIPLE:
  "Just-in-Time MCP activation with research intelligence backing"
  "Mandatory 3-MCP research validation for complex implementations"
  "Context7-first approach for all technical documentation needs"
  "Performance optimization through intelligent MCP chain selection"
  "Zero direct system access - MCP mandatory with research intelligence"
```

## 🧠 INTELLIGENT MCP ROUTING & RESEARCH INTEGRATION

### **Complexity-Based MCP & Research Selection (Modular)**
```yaml
INTELLIGENT_MCP_RESEARCH_ROUTING:
  trigger_detection:
    source: ".github/config/trigger-matrix.yaml"
    algorithm: "APEX V5.0 complexity scoring"
    optimization: "Load only when complexity threshold met (≥3.1)"
    
  complexity_routing:
    L1_simple: "1.0-3.0 → desktop-commander only (no research - bypass)"
    L2_moderate: "3.1-5.5 → context7 + desktop-commander"
    L3_complex: "5.6-7.5 → context7 + tavily + sequential-thinking + desktop-commander"
    L4_enterprise: "7.6-10.0 → ALL 5 MCPs (context7 + tavily + exa + sequential + desktop)"
    
  research_automation:
    auto_triggers:
      - "≥5 complexity → Automatic 3-MCP research activation"
      - "Unfamiliar technology → Context7 + expert validation"
      - "Security concerns → Enhanced validation requirements" 
      - "Performance critical → Benchmarking and optimization research"
      
  context7_priority:
    always_first: "Context7 MCP MANDATORY FIRST for any technical query"
    documentation_focus: "Official docs, API references, best practices"
    version_awareness: "Auto-detect library versions for accurate documentation"
    integration: "Seamless integration with Tavily and Exa for comprehensive coverage"
```

## ⚡ MODULAR PERFORMANCE & CONTEXT OPTIMIZATION

### **Smart Context Engineering (85%+ Optimization)**
```yaml
MODULAR_CONTEXT_OPTIMIZATION:
  activation_strategy: "Trigger-based loading - only when MCP value expected"
  performance_targets:
    context_loading: "<100ms for MCP modules"
    mcp_execution: "<30s for standard operations"
    research_sequence: "<45s for full 3-MCP research"
    cache_efficiency: "≥90% for Context7 documentation"
    
  intelligent_bypassing:
    simple_queries: "Skip MCP for basic definitions, simple math, explanations"
    direct_knowledge: "Use direct knowledge for common programming concepts"
    documentation_queries: "Always use Context7 for technical documentation"
    implementation_queries: "Always use full MCP chain for implementation"
    
  context_engineering:
    smart_caching: "Cache Context7 results for 5 minutes"
    parallel_execution: "Execute Tavily + Exa in parallel when possible"
    relevance_scoring: "Prioritize most relevant MCP results"
    token_optimization: "Compress context while maintaining quality"
```

## 🔒 MODULAR ENFORCEMENT & VALIDATION

### **Trigger-Based Quality Gates**
```yaml
MODULAR_ENFORCEMENT_PROTOCOL:
  activation_validation:
    trigger_accuracy: "≥95% correct trigger detection"
    complexity_assessment: "≥90% accurate complexity scoring"
    orchestrator_selection: "100% correct delegation to specialized orchestrators"
    
  execution_enforcement:
    mcp_compliance: "100% MCP usage for file/terminal operations"
    research_completeness: "100% 3-MCP sequence for complexity ≥5"
    quality_validation: "≥9.8/10 quality threshold enforcement"
    context7_priority: "Context7 ALWAYS used first for technical queries"
    
  performance_validation:
    load_time_monitoring: "Context loading time <100ms"
    execution_time_tracking: "MCP execution time monitoring"
    cache_hit_tracking: "Cache efficiency ≥90%"
    optimization_metrics: "85%+ context reduction verification"
    
MODULAR_VIOLATION_RESPONSE:
  trigger_miss: "Escalate complexity assessment if triggers missed"
  mcp_bypass_attempt: "❌ IMMEDIATE HALT - mandatory MCP usage"
  research_skip: "❌ FORCE 3-MCP research for complexity ≥5"
  quality_degradation: "❌ ENHANCE until ≥9.8/10 achieved"
  performance_degradation: "Switch to more efficient MCP chain"
```

## 🚀 MODULAR INTEGRATION PROTOCOLS

### **Orchestrator Coordination (Hub ↔ Specialized)**
```yaml
HUB_INTEGRATION:
  source_orchestrator: "copilot-instructions.md"
  delegation_trigger: "MCP/research complexity ≥3.1"
  context_handoff: "Full context + complexity assessment"
  quality_continuity: "≥9.8/10 maintained during handoff"
  
SPECIALIZED_INTEGRATION:
  target_orchestrator: "voidbeast-modular.chatmode.md"
  specialization: "GitHub Copilot mastery + advanced MCP orchestration"
  research_enhancement: "Advanced 3-MCP research coordination"
  suggestion_optimization: "Real-time GitHub Copilot suggestion enhancement"
  
GITHUB_COPILOT_INTEGRATION:
  native_enhancement: "Real-time suggestion improvement through MCP research"
  context_injection: "Smart context from Context7 documentation"
  quality_boost: "Research-backed suggestions ≥9.8/10"
  performance_optimization: "Efficient context loading + caching"
```

---

## 🎯 STATUS: MODULAR MCP & RESEARCH V6.0 READY

**Activation**: 🟢 **TRIGGER-BASED** - Smart complexity detection (≥3.1)  
**Integration**: Hub ↔ Specialized orchestrator coordination  
**Performance**: 85%+ optimization | Quality: ≥9.8/10 | Context7-first  
**Next**: Seamless orchestration with development patterns and system architecture modules