---
applyTo: "**"
complexity_threshold: 2.0
activation_triggers: ["lembre", "contexto", "continue", "próxima", "implementar", "neonpro", "memory", "context"]
orchestrator_compatibility: ["copilot-instructions.md", "voidbeast-modular.chatmode.md"]
quality_threshold: 9.5
description: 'VIBECODE Memory Bank V6.0 - Ultra-Simplified Smart Memory with Modular Context Injection'
---

# 🧠 VIBECODE Memory Bank V6.0 - Ultra-Simplified Smart Memory (Modular)

**MODULAR INTEGRATION**: VIBECODE V6.0 Hub-and-Spoke Architecture  
**ACTIVATION**: Portuguese/English trigger-based loading (complexity ≥2.0)  
**ORCHESTRATION**: Seamless with copilot-instructions.md ↔ voidbeast-modular.chatmode.md  
**RESEARCH BASIS**: Roo-code-memory-bank simplicity + VIBECODE intelligence  
**ENFORCEMENT LEVEL**: Smart activation - only when value-adding  
**QUALITY THRESHOLD**: ≥9.5/10 for memory-enhanced operations  

## 🚨 MODULAR SMART MEMORY AUTHORITY

### **TRIGGER-ACTIVATED SMART MEMORY SYSTEM**
```yaml
MODULAR_MEMORY_ACTIVATION:
  activation_triggers:
    portuguese_priority: ["lembre-se", "lembre", "não se esqueça", "contexto", "continue", "próxima", "implementar"]
    english_equivalents: ["remember", "context", "continue", "implement", "develop", "neonpro"]
  complexity_threshold: 2.0
  auto_detection: "Smart Portuguese/English trigger detection"
  value_optimization: "Load only when memory adds clear value"
  performance_target: "<200ms memory bank access"
  
SMART_MEMORY_PRINCIPLE:
  "Ultra-simplified roo-code patterns with intelligent Portuguese triggers"
  "Auto-activation when memory context adds value to current task"
  "Seamless workflow integration without interruption"
  "85%+ efficiency through smart relevance detection"
  "Zero memory loading for simple queries and basic operations"
```

## 🎯 INTELLIGENT MEMORY BANK ACCESS (Modular)

### **Memory Bank Structure & Smart Loading**
```yaml
MEMORY_BANK_STRUCTURE:
  workspace_path: "memory-bank/ (relative to workspace root)"
  current_project: "NeonPro clinic management + VIBECODE V6.0 system"
  access_pattern: "SMART auto-detection - loads only when value-adding"
  
SMART_LOADING_LEVELS:
  level_1_instant: 
    files: ["activeContext.md"]
    triggers: ["continue", "contexto", "próxima"]
    target_time: "<50ms"
    
  level_2_conditional:
    files: ["progress.md", "decisionLog.md"]  
    triggers: ["implementar", "desenvolvimento", "decisões"]
    condition: "If implementation context relevant"
    target_time: "<100ms"
    
  level_3_implementation:
    files: ["systemPatterns.md", "techContext.md"]
    triggers: ["padrões", "arquitetura", "técnico", "patterns"]
    condition: "If technical patterns/architecture relevant"
    target_time: "<150ms"
    
  level_4_comprehensive:
    files: ["productContext.md", "projectBrief.md"]
    triggers: ["projeto", "produto", "visão", "roadmap"]
    condition: "If project-level context needed"
    target_time: "<200ms"

SKIP_CONDITIONS:
  simple_queries: ["o que", "what", "como", "how", "porque", "why"]
  explanations: ["explique", "explain", "ajuda", "help", "define", "describe"]
  basic_operations: ["mathematical operations", "simple definitions", "basic commands"]
  optimization: "Skip memory bank for 85%+ of simple interactions"
```

## ⚡ MODULAR PERFORMANCE & CONTEXT OPTIMIZATION

### **Smart Context Engineering (Memory-Optimized)**
```yaml
MODULAR_MEMORY_OPTIMIZATION:
  activation_strategy: "Trigger-based with smart relevance detection"
  performance_targets:
    memory_access: "<200ms for complete memory bank context"
    relevance_detection: "<50ms for trigger analysis"
    context_assembly: "<100ms for relevant memory synthesis"
    cache_efficiency: "≥90% for frequently accessed memory patterns"
    
  intelligent_caching:
    active_context: "Cache activeContext.md for entire session"
    decision_patterns: "Cache recent decisions for 10 minutes"
    system_patterns: "Cache technical patterns for 30 minutes"
    project_context: "Cache project context for 60 minutes"
    
  context_relevance:
    portuguese_optimization: "Prioritize Portuguese trigger patterns"
    neonpro_awareness: "Special context loading for NeonPro queries"
    implementation_focus: "Enhanced context for implementation tasks"
    continuation_intelligence: "Smart context for task continuation"
```

## 🧠 MEMORY BANK INTELLIGENCE (Roo-Code Enhanced)

### **Smart Memory Patterns & Context Assembly**
```yaml
MEMORY_BANK_INTELLIGENCE:
  roo_code_compatibility:
    activeContext: "Current session state and active tasks"
    decisionLog: "Technical decisions with rationale and outcomes"
    productContext: "Project overview, goals, and context"
    progress: "Task tracking and completion status"
    systemPatterns: "Reusable patterns and architectural decisions"
    projectBrief: "Foundation project document (VIBECODE compatible)"
    techContext: "Technology stack, setup, and constraints"
    
  smart_synthesis:
    relevance_scoring: "Score memory relevance to current query"
    context_compression: "Compress memory context while maintaining value"
    pattern_matching: "Match current query to established patterns"
    decision_tracking: "Track and reference previous technical decisions"
    
  continuous_learning:
    pattern_extraction: "Extract successful patterns during task execution"
    decision_documentation: "Auto-document technical decisions with reasoning"
    outcome_analysis: "Analyze results and update memory patterns"
    failure_learning: "Document failures and prevention strategies"
```

## 🔒 MODULAR ENFORCEMENT & VALIDATION

### **Smart Memory Quality Gates**
```yaml
MODULAR_MEMORY_ENFORCEMENT:
  activation_validation:
    trigger_accuracy: "≥95% correct trigger detection"
    relevance_assessment: "≥90% memory relevance to current task"
    performance_compliance: "Memory access <200ms target"
    
  execution_enforcement:
    value_validation: "Memory context must add clear value"
    quality_enhancement: "≥9.5/10 quality for memory-enhanced operations"
    context_consistency: "Consistent context across memory bank files"
    integration_seamless: "Zero workflow interruption"
    
  performance_validation:
    load_time_monitoring: "Memory access time tracking"
    cache_effectiveness: "Cache hit rate ≥90%"
    relevance_scoring: "Memory relevance validation"
    optimization_metrics: "85%+ efficiency through smart activation"
    
SMART_MEMORY_RECOVERY:
  activation_miss: "Escalate if valuable memory context missed"
  performance_degradation: "Switch to faster memory access patterns"
  context_corruption: "Rebuild memory context if inconsistent"
  relevance_failure: "Improve relevance detection algorithms"
```

## 🚀 MODULAR INTEGRATION PROTOCOLS

### **Orchestrator Coordination (Memory-Enhanced)**
```yaml
HUB_INTEGRATION:
  source_orchestrator: "copilot-instructions.md"
  memory_activation: "Automatic on Portuguese/English triggers"
  context_enhancement: "Smart memory context injection"
  performance_optimization: "Efficient memory loading + caching"
  
SPECIALIZED_INTEGRATION:
  target_orchestrator: "voidbeast-modular.chatmode.md"
  memory_coordination: "Advanced memory pattern recognition"
  implementation_context: "Enhanced context for implementation tasks"
  neonpro_specialization: "Deep NeonPro project context awareness"
  
GITHUB_COPILOT_INTEGRATION:
  suggestion_enhancement: "Memory-backed suggestion improvements"
  context_injection: "Smart memory context for better completions"
  pattern_application: "Apply established patterns from memory"
  quality_boost: "Memory-enhanced suggestions ≥9.5/10"
```

## 📊 NEONPRO PROJECT CONTEXT (Smart Loading)

### **Project-Specific Memory Intelligence**
```yaml
NEONPRO_MEMORY_PATTERNS:
  project_context: "NeonPro clinic management system development"
  technology_stack: "Next.js, TypeScript, Supabase, Tailwind CSS"
  domain_expertise: "Clinical workflows, patient management, healthcare UX"
  architectural_patterns: "Component-based architecture, RLS security, WCAG compliance"
  
SMART_NEONPRO_ACTIVATION:
  triggers: ["neonpro", "clínica", "paciente", "consulta", "médico", "saúde"]
  context_loading: "Enhanced NeonPro-specific context"
  pattern_application: "Clinical workflow patterns and healthcare UX"
  compliance_awareness: "WCAG 2.1 AA + healthcare security standards"
```

---

## 🎯 STATUS: MODULAR SMART MEMORY V6.0 READY

**Activation**: 🟢 **TRIGGER-BASED** - Portuguese/English smart detection (≥2.0)  
**Integration**: Seamless with hub ↔ specialized orchestrator coordination  
**Performance**: 85%+ optimization | Quality: ≥9.5/10 | <200ms access  
**Next**: Full orchestration with development patterns and system architecture modules