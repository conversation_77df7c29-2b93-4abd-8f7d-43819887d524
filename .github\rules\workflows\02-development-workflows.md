---
alwaysApply: true
description: 'Development Workflows - Architecture, Features, Bugs, Refactoring'
version: '5.0'
title: 'Development Workflows - Complete Development Lifecycle'
type: 'development-workflows'
mcp_servers: ['context7-mcp', 'sequential-thinking', 'desktop-commander']
quality_threshold: 9.6
specialization: 'development-lifecycle'
trigger_keywords: ['architecture', 'feature', 'bug', 'refactor', 'develop', 'implement', 'design', 'fix']
enforcement_level: 'absolute'
approach: 'comprehensive-development'
framework_focus: 'development-excellence'
priority: 8
---

# 🚀 DEVELOPMENT WORKFLOWS - COMPLETE LIFECYCLE

## 🏗️ ARCHITECTURE WORKFLOWS

### **Activation Triggers**
```yaml
ARCHITECTURE_TRIGGERS:
  keywords: ["architecture", "design", "system", "structure", "patterns", "scalability"]
  complexity_range: [7, 8, 9, 10]
  context_indicators: ["microservices", "monolith", "api", "database", "infrastructure"]
```

### **System Design Process**
```yaml
ARCHITECTURE_WORKFLOW:
  step_1_requirements_analysis:
    - "Gather functional and non-functional requirements"
    - "Identify stakeholders and their concerns"
    - "Define system boundaries and constraints"
    - "Establish quality attributes (performance, security, scalability)"
    
  step_2_architectural_research:
    - "Research industry best practices and patterns via Context7 + Tavily"
    - "Analyze similar system architectures"
    - "Evaluate technology stack options with Context7 documentation"
    - "Consider architectural trade-offs and decisions"
    
  step_3_high_level_design:
    - "Create system architecture overview"
    - "Define major components and their interactions"
    - "Establish data flow and communication patterns"
    - "Design deployment and infrastructure strategy"
    
  step_4_detailed_design:
    - "Design individual component architectures"
    - "Define APIs and interface contracts"
    - "Create database schema and data models"
    - "Plan security and authentication mechanisms"
    
  step_5_validation_and_review:
    - "Validate design against requirements"
    - "Conduct architecture review sessions"
    - "Create prototypes for critical components"
    - "Document architectural decisions and rationale"

ARCHITECTURE_QUALITY_GATES:
  requirements_completeness: "≥95% requirements coverage and validation"
  research_backing: "Architecture decisions backed by Context7 + research"
  design_consistency: "Consistent design patterns and principles"
  documentation_quality: "≥9.6/10 architecture documentation quality"
```

## 🚀 FEATURE DEVELOPMENT WORKFLOWS

### **Pre-Development Phase**
```yaml
FEATURE_PREPARATION:
  requirements_analysis: "Understand complete feature scope and acceptance criteria"
  architecture_review: "Assess impact on existing system architecture"
  dependency_analysis: "Identify required dependencies and integrations"
  resource_planning: "Estimate development time and complexity"
  research_validation: "Research best practices via Context7 for feature type"
```

### **Development Standards**
```yaml
DEVELOPMENT_STANDARDS:
  incremental_development: "Break features into manageable, testable components"
  test_driven_development: "Write tests alongside implementation (TDD/BDD)"
  code_reviews: "Regular code review checkpoints with quality validation"
  documentation: "Document decisions and implementation details"
  context7_validation: "Validate implementation against Context7 best practices"
```

### **Implementation Checklist**
```yaml
FEATURE_CHECKLIST:
  planning:
    - "✅ Feature requirements clearly defined and validated"
    - "✅ Architecture impact assessed and documented"
    - "✅ Dependencies identified and planned"
    - "✅ Test strategy defined with comprehensive coverage"
    
  implementation:
    - "✅ Implementation broken into incremental steps"
    - "✅ Performance implications considered and optimized"
    - "✅ Security considerations addressed and validated"
    - "✅ Code quality meets ≥9.6/10 standard"
    
  validation:
    - "✅ Unit testing with comprehensive coverage"
    - "✅ Integration testing with existing system"
    - "✅ User acceptance criteria met"
    - "✅ Performance benchmarks satisfied"
    - "✅ Security review completed and validated"
```

## 🐛 BUG FIXING & ISSUE RESOLUTION

### **Issue Analysis Protocol**
```yaml
BUG_ANALYSIS_WORKFLOW:
  step_1_identification:
    problem_definition: "Clearly define the issue and its symptoms"
    environment_analysis: "Document environment and reproduction conditions"
    impact_assessment: "Evaluate severity and user impact"
    urgency_classification: "Classify urgency and priority level"
    
  step_2_investigation:
    reproduction: "Always reproduce the issue before attempting fixes"
    root_cause_analysis: "Investigate underlying causes, not just symptoms"
    systematic_debugging: "Use debugging tools and logging effectively"
    component_isolation: "Isolate the problem to specific components"
    
  step_3_solution_planning:
    solution_design: "Plan fix with minimal disruption"
    impact_analysis: "Analyze potential side effects of the fix"
    testing_strategy: "Plan comprehensive testing approach"
    rollback_planning: "Prepare rollback strategy if needed"
```

### **Fix Implementation Standards**
```yaml
BUG_FIX_STANDARDS:
  minimal_changes: "Make the smallest change that fixes the issue"
  functionality_preservation: "Ensure fix doesn't break existing features"
  test_coverage: "Add tests to prevent regression"
  code_review: "Mandatory peer review for all fixes"
  context7_validation: "Validate fix against Context7 best practices"

BUG_VALIDATION_CHECKLIST:
  pre_fix:
    - "✅ Issue clearly reproduced and understood"
    - "✅ Root cause identified and documented"
    - "✅ Solution approach planned and reviewed"
    
  post_fix:
    - "✅ Solution tested in isolation"
    - "✅ Regression tests added and passing"
    - "✅ Related functionality verified"
    - "✅ Performance impact assessed"
    - "✅ Documentation updated"
```

## 🔧 REFACTORING & CODE IMPROVEMENT

### **Refactoring Strategy**
```yaml
REFACTORING_WORKFLOW:
  step_1_analysis:
    improvement_identification: "Identify code smells, performance bottlenecks, maintainability issues"
    impact_assessment: "Assess refactoring impact and complexity"
    priority_classification: "Prioritize refactoring tasks by value and risk"
    
  step_2_planning:
    incremental_planning: "Plan refactoring in safe, incremental steps"
    behavior_preservation: "Ensure functionality remains unchanged"
    test_safety_net: "Establish comprehensive tests before refactoring"
    
  step_3_execution:
    version_control: "Commit frequently during refactoring process"
    incremental_changes: "Make small, verifiable changes"
    continuous_testing: "Run tests after each incremental change"
    rollback_readiness: "Maintain clear rollback strategy"
```

### **Common Refactoring Patterns**
```yaml
REFACTORING_PATTERNS:
  component_extraction: "Break large components into smaller, focused ones"
  function_extraction: "Separate concerns and improve readability"
  logic_simplification: "Reduce complexity and improve maintainability"
  performance_optimization: "Address performance bottlenecks systematically"
  pattern_standardization: "Align with established patterns and standards"

REFACTORING_QUALITY_IMPROVEMENTS:
  readability: "Make code more readable and self-documenting"
  performance: "Optimize for better performance where needed"
  maintainability: "Improve code structure for easier maintenance"
  standards_compliance: "Align with current coding standards and best practices"
  context7_alignment: "Ensure alignment with Context7 recommended patterns"
```

### **Refactoring Validation Process**
```yaml
REFACTORING_VALIDATION:
  pre_refactoring:
    - "✅ Tests pass before refactoring begins"
    - "✅ Comprehensive test coverage established"
    - "✅ Refactoring plan documented and reviewed"
    
  during_refactoring:
    - "✅ Incremental changes tested at each step"
    - "✅ Behavior preservation validated continuously"
    - "✅ Performance impact monitored"
    
  post_refactoring:
    - "✅ All tests pass with improved code"
    - "✅ Performance benchmarks maintained or improved"
    - "✅ Code review completed and approved"
    - "✅ Documentation updated"
    - "✅ Team knowledge transfer completed"
```

## 🎯 DEVELOPMENT QUALITY ENFORCEMENT

### **Universal Development Standards**
```yaml
DEVELOPMENT_QUALITY_GATES:
  code_quality: "≥9.6/10 code quality across all development activities"
  test_coverage: "≥90% test coverage for all new and modified code"
  performance_standards: "No performance degradation without explicit approval"
  security_compliance: "Security review mandatory for all changes"
  documentation_quality: "Complete and accurate documentation required"

CONTEXT7_INTEGRATION:
  pattern_validation: "All code patterns validated against Context7 documentation"
  best_practice_compliance: "Implementation follows Context7 best practices"
  api_usage_verification: "API usage verified against Context7 current standards"
  optimization_application: "Context7 optimization recommendations applied"

HEALTHCARE_SPECIFIC_REQUIREMENTS:
  patient_safety: "❌ CRITICAL: Patient safety impact assessment for all changes"
  regulatory_compliance: "❌ MANDATORY: LGPD/ANVISA compliance validation"
  data_protection: "✅ MANDATORY: Data protection measures implementation"
  audit_trail: "✅ MANDATORY: Complete audit trail for all development activities"
```

### **Continuous Improvement Protocol**
```yaml
CONTINUOUS_IMPROVEMENT:
  pattern_learning: "Extract and document successful development patterns"
  performance_optimization: "Continuous performance monitoring and optimization"
  quality_enhancement: "Regular quality metric analysis and improvement"
  knowledge_sharing: "Team knowledge sharing and best practice documentation"
  context7_evolution: "Stay current with Context7 documentation updates"
```

---

**🚀 DEVELOPMENT WORKFLOWS EXCELLENCE**
*Architecture: Research-Backed | Features: Quality-Driven | Bugs: Root-Cause Analysis*
*Refactoring: Safety-First | Quality: ≥9.6/10 | Context7: Integrated*