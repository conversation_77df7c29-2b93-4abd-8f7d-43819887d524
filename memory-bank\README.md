# VIBECODE Memory Bank V5.0 - Ultra-Simplified Smart Memory (2025 Roo-Code Enhanced)

**INTEGRATION**: Roo-code-memory-bank simplicity + VIBECODE intelligence + Portuguese triggers
**RESEARCH BASIS**: Ultra-simplified architecture with smart activation and auto-optimization

Estrutura ultra-simplificada inspirada no [roo-code-memory-bank](https://github.com/roocode-org/roo-code-memory-bank) com inteligência VIBECODE e triggers inteligentes em português/inglês. Ativação automática apenas quando adiciona valor ao workflow.

## 🚀 ESTRUTURA ULTRA-SIMPLES (Roo-<PERSON> Pattern)

```
memory-bank/
├── 📄 Core Files (Essenciais - Raiz)
│   ├── activeContext.md      # Estado atual do desenvolvimento
│   ├── productContext.md     # Visão e objetivos do produto
│   ├── progress.md           # Tracking de progresso
│   ├── decisionLog.md        # Log de decisões técnicas
│   ├── projectBrief.md       # Documento fundação do projeto
│   ├── systemPatterns.md     # Padrões e best practices
│   └── techContext.md        # Stack técnico e setup
│
├── 🛠️ Smart Tools (Comandos Simples)
│   ├── umb.ps1              # Comando UMB para PowerShell
│   ├── umb.js               # Comando UMB para Node.js
│   └── smart-memory.js      # Sistema de carregamento inteligente
│
└── 📁 .memory-bank/ (Complexidade Opcional)
    ├── smart-config.json    # Configuração inteligente
    ├── core/               # Sistemas avançados (Python)
    ├── config/             # Configurações antigas
    ├── logs/               # Logs do sistema
    ├── storage/            # Armazenamento persistente
    └── requirements.txt    # Dependências Python (opcional)
```

## ⚡ USO ULTRA-SIMPLES

### Comando UMB (Inspirado no roo-code)
```powershell
# PowerShell (Windows)
.\umb.ps1           # Atualiza memory bank
.\umb.ps1 status    # Mostra status
.\umb.ps1 optimize  # Otimiza performance

# Node.js (Cross-platform)
node umb.js         # Atualiza memory bank
node umb.js status  # Mostra status
node umb.js optimize # Otimiza performance
```

## 🧠 SMART TRIGGERS (AUTO-ATIVAÇÃO)

**ATIVAÇÃO AUTOMÁTICA BASEADA EM KEYWORDS:**

### 🇧🇷 Triggers em Português (Alta Prioridade)
- **Memória**: `lembre-se`, `lembre`, `não se esqueça`, `não esqueça`, `relembre`
- **Continuação**: `continue`, `continuar`, `prosseguir`, `seguir`, `próxima`, `próximo`
- **Implementação**: `implementar`, `implementação`, `desenvolver`, `desenvolvimento`, `criar`, `construir`
- **Contexto**: `contexto`, `histórico`, `decisões`, `padrões`
- **NeonPro**: `neonpro`, `clínica`, `médico`, `saúde`
- **Debug**: `debug`, `debugar`, `corrigir`, `otimizar`, `melhorar`, `problema`, `erro`
- **Arquitetura**: `documentar`, `padrão`, `arquitetura`, `design`, `estrutura`

### 🇺🇸 English Equivalents
- `remember`, `context`, `continue`, `implement`, `develop`, `create`, `debug`, `optimize`

### ⚡ Performance Inteligente
- **Skip Conditions**: `o que`, `what`, `como`, `how`, `porque`, `why`, `explique`, `explain`
- **Load Time**: `<50ms` para activeContext.md (sempre)
- **Conditional Load**: `<200ms` para contexto completo (quando relevante)
- **Cache Hit Rate**: `>90%` para padrões comuns

## 📋 ARQUIVOS CORE (Sempre Simples)

### 🎯 activeContext.md
Estado atual do desenvolvimento, próximas tarefas, foco principal

### 💼 productContext.md  
Visão do produto, objetivos, usuários-alvo, problemas resolvidos

### 📈 progress.md
O que está funcionando, o que foi completado, o que resta fazer

### 📋 decisionLog.md
Decisões técnicas com rationale, alternativas consideradas, resultados

### 📋 projectBrief.md
Documento fundação - escopo, objetivos, source of truth do projeto

### 🏗️ systemPatterns.md
Padrões arquiteturais, decisões de design, relacionamentos entre componentes

### ⚙️ techContext.md
Tecnologias, setup, dependências, constraints técnicos

## 🛠️ WORKFLOW INTELIGENTE

### Modo Manual
```powershell
# Ler contexto essencial
Get-Content memory-bank/activeContext.md
Get-Content memory-bank/projectBrief.md

# Atualizar progresso
.\umb.ps1 update

# Ver status completo
.\umb.ps1 status
```

### Modo Automático (Smart Triggers)
- **Auto-carrega** quando detecta keywords relevantes
- **Cache inteligente** para performance
- **Lazy loading** baseado em valor agregado
- **Self-improvement** contínuo baseado em uso

## 🔧 INTEGRAÇÃO COM WORKFLOWS

### Para Todas as Rules e Instructions
```markdown
# Exemplo de integração em qualquer rule:

## Smart Memory Bank Integration
**TRIGGERS**: implementar, continuar, neonpro, debug, otimizar
**AUTO-LOAD**: activeContext.md (sempre <50ms)
**CONDITIONAL**: progress.md, decisionLog.md (se relevante)
**PERFORMANCE**: <200ms total load time

## Usage Pattern
1. Triggers detectados automaticamente
2. Contexto carregado de forma inteligente  
3. Informações aplicadas ao workflow
4. Learning documentado automaticamente
```

## 📊 MÉTRICAS DE PERFORMANCE

### Targets de Performance (2025)
- **Context Load**: <200ms (vs. 2s+ anterior)
- **Token Reduction**: 85%+ via smart loading
- **Cache Hit Rate**: >90% para padrões comuns
- **Memory Efficiency**: Lazy loading apenas quando necessário
- **Auto-optimization**: Continuous improvement baseado em usage

### Quality Gates
- **Information Quality**: ≥8/10 synthesis de múltiplas fontes
- **Relevance Threshold**: ≥95% contexto diretamente aplicável
- **Update Currency**: Informação reflete estado atual do projeto
- **Cross-validation**: Anti-hallucination via referência cruzada

## 🎯 PROJETO NEONPRO (Especialização)

### Context Automático para NeonPro
- **Triggers**: `neonpro`, `clínica`, `médico`, `saúde`, `LGPD`, `ANVISA`
- **Stack**: Next.js 15, TypeScript, Supabase, Tailwind CSS
- **Compliance**: LGPD, ANVISA, CFM automático
- **Quality**: ≥9.5/10 para sistemas médicos

### Patterns NeonPro
- Gestão de clínicas de estética e beleza
- Compliance médico brasileiro automático
- Performance <200ms para UX médica
- Integração Supabase com RLS automático

## 🚀 QUICK START

1. **Usar Triggers Naturais**: Use palavras como "lembre-se de implementar", "continue com", "próxima tarefa"
2. **Comando UMB**: `.\umb.ps1` para updates manuais
3. **Auto-optimization**: Sistema aprende e melhora automaticamente
4. **Zero Config**: Funciona out-of-the-box com VIBECODE

---

**VIBECODE Memory Bank V5.0** - Simplicidade roo-code + Inteligência VIBECODE + Triggers em Português
**Performance**: <200ms | **Quality**: ≥8/10 | **Smart**: Auto-activation | **Simple**: Roo-code pattern
