---
alwaysApply: true
description: 'Security Baseline Standards - Comprehensive Security Authority'
version: '7.0'
title: 'Security Baseline Standards - Complete Security Framework'
type: 'security-baseline-authority'
mcp_servers: ['sequential-thinking', 'context7-mcp', 'tavily-mcp', 'exa-mcp', 'desktop-commander']
quality_threshold: 9.8
specialization: 'comprehensive-security-authority'
trigger_keywords: ['security', 'authentication', 'authorization', 'encryption', 'compliance', 'data protection', 'vulnerability', 'audit', 'rls', 'database security']
enforcement_level: 'absolute'
approach: 'security-first-comprehensive'
globs: '**/*'
priority: 1
---

# 🔐 Security Baseline Standards - Comprehensive Security Authority

## 🚨 ABSOLUTE SECURITY REQUIREMENTS

### **Security-First Development Philosophy**

```yaml
SECURITY_FIRST_MANDATE:
  security_by_design: "Security integrated from initial design phase, not added later"
  zero_trust_architecture: "Never trust, always verify - comprehensive validation framework"
  defense_in_depth: "Multiple security layers with overlapping protection mechanisms"
  principle_of_least_privilege: "Minimum necessary access rights for all components"
  secure_by_default: "Default configurations must be secure, not convenient"
  
ABSOLUTE_SECURITY_STANDARDS:
  authentication_mandatory: "All endpoints require proper authentication - NO EXCEPTIONS"
  authorization_granular: "Fine-grained authorization controls for all resources"
  encryption_everywhere: "End-to-end encryption for all sensitive data transmission"
  input_validation_comprehensive: "All inputs validated, sanitized, and type-checked"
  audit_logging_complete: "Comprehensive audit trails for all security-relevant operations"
```

## 🛡️ DATA PROTECTION & PRIVACY

### **Comprehensive Data Protection Framework**

```yaml
DATA_PROTECTION_REQUIREMENTS:
  data_classification:
    public_data: "No special protection required, standard handling"
    internal_data: "Access controls and basic encryption required"
    confidential_data: "Strong encryption and strict access controls"
    restricted_data: "Maximum security - encryption, audit, approval workflow"
    
  encryption_standards:
    at_rest: "AES-256 encryption for all stored sensitive data"
    in_transit: "TLS 1.3 minimum for all data transmission"
    in_processing: "Secure enclaves or encrypted processing where applicable"
    key_management: "Hardware security modules or equivalent for key storage"
    
  privacy_compliance:
    data_minimization: "Collect only necessary data for specific purposes"
    purpose_limitation: "Use data only for stated and legitimate purposes"
    storage_limitation: "Retain data only as long as necessary"
    accuracy_maintenance: "Ensure data accuracy and enable corrections"
    
  data_lifecycle_management:
    collection_controls: "Explicit consent and purpose specification"
    processing_safeguards: "Secure processing with audit trails"
    storage_protection: "Encrypted storage with access controls"
    retention_policies: "Automated deletion based on retention periods"
    disposal_security: "Secure deletion with verification"
```

### **Healthcare Data Protection (LGPD/ANVISA Compliance)**

```yaml
HEALTHCARE_COMPLIANCE_REQUIREMENTS:
  lgpd_brazilian_compliance:
    lawful_basis: "Explicit consent or legitimate interest documented"
    data_subject_rights: "Access, rectification, deletion, portability implemented"
    data_protection_officer: "Designated DPO with defined responsibilities"
    impact_assessments: "DPIA for high-risk processing activities"
    breach_notification: "72-hour notification to authorities, immediate to subjects"
    
  anvisa_health_standards:
    medical_device_security: "Cybersecurity controls for medical devices"
    clinical_data_integrity: "Data integrity controls for clinical information"
    audit_requirements: "Comprehensive audit trails for regulatory compliance"
    risk_management: "Clinical risk management for patient safety"
    
  patient_data_protection:
    consent_management: "Granular consent tracking and management"
    access_controls: "Role-based access with clinical need verification"
    audit_logging: "Complete audit trails for all patient data access"
    anonymization: "De-identification for research and analytics"
    data_sharing: "Secure data sharing protocols with consent verification"
```

## 🔐 AUTHENTICATION & AUTHORIZATION

### **Comprehensive Authentication Framework**

```yaml
AUTHENTICATION_REQUIREMENTS:
  multi_factor_authentication:
    mandatory_scenarios: "All administrative access and sensitive operations"
    factor_types: "Something you know, have, are - minimum 2 factors"
    adaptive_authentication: "Risk-based authentication with context awareness"
    backup_authentication: "Secure backup methods for primary factor failure"
    
  session_management:
    secure_session_creation: "Cryptographically secure session identifiers"
    session_timeout: "Automatic timeout for inactive sessions"
    concurrent_session_limits: "Limit concurrent sessions per user"
    session_invalidation: "Secure session termination and cleanup"
    
  password_security:
    complexity_requirements: "Minimum 12 characters with complexity rules"
    password_history: "Prevent reuse of last 12 passwords"
    breach_detection: "Check against known breached password databases"
    secure_storage: "Salted hashing with appropriate algorithms (bcrypt, Argon2)"
    
  oauth_security:
    secure_redirect_uris: "Strictly validated redirect URIs"
    pkce_enforcement: "PKCE required for all OAuth flows"
    scope_limitation: "Minimal necessary scopes granted"
    token_security: "Secure token storage and transmission"

AUTHORIZATION_FRAMEWORK:
  role_based_access_control:
    role_definition: "Clear role definitions with specific permissions"
    permission_granularity: "Fine-grained permissions for all resources"
    role_inheritance: "Secure role hierarchy with delegation controls"
    regular_review: "Periodic access reviews and role audits"
    
  attribute_based_access_control:
    context_awareness: "Time, location, device context in authorization"
    dynamic_authorization: "Real-time authorization based on current attributes"
    policy_enforcement: "Centralized policy enforcement point"
    audit_integration: "Authorization decisions logged and auditable"
```

## 🗄️ DATABASE SECURITY

### **Comprehensive Database Protection**

```yaml
DATABASE_SECURITY_REQUIREMENTS:
  row_level_security:
    rls_mandatory: "Row Level Security enabled for ALL tables with user data"
    policy_enforcement: "Comprehensive RLS policies for read/write/delete operations"
    policy_testing: "Automated testing of RLS policies with multiple user contexts"
    performance_optimization: "RLS policies optimized for query performance"
    
  connection_security:
    encrypted_connections: "SSL/TLS required for all database connections"
    connection_pooling: "Secure connection pooling with authentication"
    network_isolation: "Database servers isolated in private networks"
    firewall_rules: "Restrictive firewall rules for database access"
    
  query_security:
    parameterized_queries: "Prepared statements for all dynamic queries"
    sql_injection_prevention: "Input validation and parameterization mandatory"
    query_monitoring: "Monitor and log all database queries for anomalies"
    performance_monitoring: "Query performance monitoring for DoS detection"
    
  backup_security:
    encrypted_backups: "All backups encrypted with separate encryption keys"
    backup_access_controls: "Strict access controls for backup files"
    backup_testing: "Regular backup restoration testing"
    retention_policies: "Secure backup retention and disposal policies"

SUPABASE_SPECIFIC_SECURITY:
  authentication_integration:
    supabase_auth: "Leverage Supabase Auth with proper configuration"
    jwt_validation: "Proper JWT token validation and refresh"
    user_management: "Secure user registration and profile management"
    social_auth_security: "Secure social authentication with minimal permissions"
    
  real_time_security:
    channel_authorization: "Authorization required for all real-time channels"
    message_validation: "Validate all real-time messages for security"
    rate_limiting: "Rate limiting for real-time subscriptions"
    connection_monitoring: "Monitor real-time connections for anomalies"
```

## 🌐 API SECURITY

### **Comprehensive API Protection**

```yaml
API_SECURITY_REQUIREMENTS:
  authentication_authorization:
    bearer_token_validation: "Validate all bearer tokens with proper algorithms"
    api_key_management: "Secure API key generation, rotation, and revocation"
    scope_validation: "Validate API access scopes for each request"
    rate_limiting: "Implement rate limiting to prevent abuse"
    
  input_validation:
    request_validation: "Validate all request parameters and body content"
    content_type_validation: "Validate and restrict content types"
    size_limitations: "Enforce request size limits to prevent DoS"
    injection_prevention: "Prevent all forms of injection attacks"
    
  output_security:
    response_filtering: "Filter sensitive data from API responses"
    error_handling: "Secure error messages without information disclosure"
    cors_configuration: "Properly configured CORS with restrictive policies"
    security_headers: "Comprehensive security headers in all responses"
    
  monitoring_logging:
    access_logging: "Log all API access with user context"
    anomaly_detection: "Monitor for unusual API usage patterns"
    threat_detection: "Real-time threat detection and response"
    performance_monitoring: "Monitor API performance for DoS detection"

NEXTJS_SPECIFIC_SECURITY:
  server_side_security:
    api_route_protection: "Authentication required for all API routes"
    middleware_security: "Security middleware for request processing"
    environment_protection: "Secure environment variable management"
    build_security: "Secure build process and dependency management"
    
  client_side_security:
    csp_implementation: "Content Security Policy for XSS prevention"
    sri_implementation: "Subresource Integrity for external resources"
    secure_storage: "Secure client-side storage of sensitive data"
    xss_prevention: "Comprehensive XSS prevention measures"
```

## 🔍 VULNERABILITY MANAGEMENT

### **Comprehensive Vulnerability Assessment Framework**

```yaml
VULNERABILITY_MANAGEMENT:
  threat_modeling:
    application_threat_modeling: "Comprehensive threat models for all applications"
    data_flow_analysis: "Security analysis of all data flows"
    attack_surface_analysis: "Regular attack surface assessment"
    threat_intelligence: "Integration with threat intelligence feeds"
    
  security_testing:
    static_analysis: "Automated static code analysis for security vulnerabilities"
    dynamic_analysis: "Runtime security testing and vulnerability scanning"
    penetration_testing: "Regular penetration testing by qualified professionals"
    dependency_scanning: "Automated scanning of all dependencies for vulnerabilities"
    
  incident_response:
    incident_classification: "Classification framework for security incidents"
    response_procedures: "Documented incident response procedures"
    communication_plans: "Communication plans for security incidents"
    forensic_capabilities: "Digital forensics capabilities for incident investigation"
    
  security_monitoring:
    siem_integration: "Security Information and Event Management integration"
    real_time_monitoring: "Real-time security monitoring and alerting"
    behavioral_analysis: "User and entity behavior analytics"
    threat_hunting: "Proactive threat hunting activities"
```

## 💡 SECURE DEVELOPMENT PRACTICES

### **Security-Integrated Development Lifecycle**

```yaml
SECURE_DEVELOPMENT_REQUIREMENTS:
  secure_coding_standards:
    coding_guidelines: "Comprehensive secure coding guidelines and standards"
    code_review_security: "Security-focused code reviews for all changes"
    security_training: "Regular security training for all developers"
    secure_libraries: "Use of security-vetted libraries and frameworks"
    
  dependency_management:
    vulnerability_scanning: "Automated scanning of all dependencies"
    dependency_approval: "Approval process for new dependencies"
    regular_updates: "Regular updates with security patch management"
    license_compliance: "License compliance and security implications"
    
  secrets_management:
    secret_detection: "Automated detection of secrets in code"
    secure_storage: "Secure storage of all secrets and credentials"
    secret_rotation: "Regular rotation of secrets and credentials"
    access_controls: "Strict access controls for secret management systems"
    
  deployment_security:
    secure_pipelines: "Security controls in CI/CD pipelines"
    infrastructure_security: "Secure infrastructure configuration"
    container_security: "Container security scanning and hardening"
    production_monitoring: "Continuous security monitoring in production"

PERFORMANCE_SECURITY_INTEGRATION:
  security_performance_balance:
    optimized_security_controls: "Security controls optimized for performance"
    caching_security: "Secure caching strategies that maintain performance"
    encryption_optimization: "Hardware-accelerated encryption where possible"
    monitoring_efficiency: "Efficient security monitoring with minimal overhead"
```

## 📋 COMPREHENSIVE COMPLIANCE CHECKLIST

### **Security Implementation Validation**

```yaml
SECURITY_IMPLEMENTATION_CHECKLIST:
  authentication_authorization:
    - [ ] Multi-factor authentication implemented for all administrative access
    - [ ] Role-based access control implemented with granular permissions
    - [ ] Session management with secure timeouts and invalidation
    - [ ] OAuth/OIDC implementation with PKCE and secure flows
    - [ ] Password policies enforced with breach detection
    
  data_protection:
    - [ ] Data classification framework implemented
    - [ ] Encryption at rest using AES-256 or equivalent
    - [ ] Encryption in transit using TLS 1.3 minimum
    - [ ] Key management using hardware security modules
    - [ ] Data retention and disposal policies implemented
    
  database_security:
    - [ ] Row Level Security enabled and tested for all user data tables
    - [ ] Database connections encrypted and network isolated
    - [ ] Parameterized queries used for all dynamic SQL
    - [ ] Database backups encrypted with separate keys
    - [ ] Database audit logging enabled and monitored
    
  api_security:
    - [ ] API authentication and authorization implemented
    - [ ] Input validation for all API endpoints
    - [ ] Rate limiting and DoS protection implemented
    - [ ] Security headers configured for all responses
    - [ ] API monitoring and anomaly detection active
    
  vulnerability_management:
    - [ ] Automated dependency vulnerability scanning
    - [ ] Static code analysis integrated into CI/CD
    - [ ] Dynamic security testing performed regularly
    - [ ] Penetration testing conducted by qualified professionals
    - [ ] Incident response plan documented and tested
    
  compliance_requirements:
    - [ ] LGPD compliance implemented for Brazilian data protection
    - [ ] ANVISA standards met for healthcare applications
    - [ ] Audit logging comprehensive and tamper-evident
    - [ ] Data subject rights implemented (access, rectification, deletion)
    - [ ] Privacy impact assessments completed for high-risk processing
    
  monitoring_logging:
    - [ ] Security Information and Event Management (SIEM) deployed
    - [ ] Real-time security monitoring and alerting active
    - [ ] User and entity behavior analytics implemented
    - [ ] Comprehensive audit trails for all security-relevant operations
    - [ ] Regular security metrics reporting and analysis
```

## 🔒 EMERGENCY SECURITY PROTOCOLS

### **Security Incident Response**

```yaml
EMERGENCY_SECURITY_PROTOCOLS:
  immediate_response:
    incident_detection: "Immediate detection and classification of security incidents"
    containment_procedures: "Rapid containment to prevent incident escalation"
    impact_assessment: "Quick assessment of incident scope and impact"
    stakeholder_notification: "Immediate notification of relevant stakeholders"
    
  investigation_forensics:
    evidence_preservation: "Digital forensics evidence preservation procedures"
    root_cause_analysis: "Comprehensive root cause analysis methodology"
    timeline_reconstruction: "Incident timeline reconstruction and analysis"
    lessons_learned: "Post-incident lessons learned and improvement implementation"
    
  recovery_restoration:
    service_restoration: "Secure service restoration procedures"
    data_recovery: "Secure data recovery from encrypted backups"
    system_hardening: "Additional security hardening based on incident analysis"
    monitoring_enhancement: "Enhanced monitoring based on incident patterns"
```

---

**🔐 SECURITY BASELINE STANDARDS - COMPREHENSIVE SECURITY AUTHORITY**
**SECURITY-FIRST + DEFENSE-IN-DEPTH + COMPLIANCE + QUALITY ≥9.8/10**
**ZERO TRUST + COMPREHENSIVE PROTECTION + REGULATORY COMPLIANCE**