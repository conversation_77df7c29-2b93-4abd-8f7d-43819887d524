#!/usr/bin/env python3
"""
VIBECODE V4.0 Modular Architecture Validation Script
==================================================

Validates the complete modular instruction architecture implementation.
Ensures all modules are correctly integrated and functional.
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

class ModularArchitectureValidator:
    def __init__(self, base_path: str = ".github"):
        self.base_path = Path(base_path)
        self.instructions_path = self.base_path / "instructions"
        self.master_file = self.base_path / "copilot-instructions.md"
        
        # Expected modules and their properties
        self.expected_modules = {
            "development-patterns.md": {
                "triggers": ["code", "implement", "build", "develop", "create", "fix", "standards", "patterns"],
                "min_lines": 250,
                "key_sections": ["coding standards", "implementation patterns", "quality gates"]
            },
            "agent-behaviors.md": {
                "triggers": ["autonomous", "agent", "orchestrator", "voidbeast", "bmad", "delegation"],
                "min_lines": 250,
                "key_sections": ["agent coordination", "autonomous behaviors", "task delegation"]
            },
            "research-protocols.md": {
                "triggers": ["research", "investigate", "analyze", "study", "compare", "validate", "expert"],
                "min_lines": 250,
                "key_sections": ["research sequence", "context7 integration", "validation frameworks"]
            },
            "system-architecture.md": {
                "triggers": ["architecture", "design", "patterns", "system", "structure", "technical", "scalability"],
                "min_lines": 250,
                "key_sections": ["architectural patterns", "design principles", "technical excellence"]
            },
            "automation-tools.md": {
                "triggers": ["automation", "tools", "mcp", "workflow", "orchestration", "integration", "process"],
                "min_lines": 250,
                "key_sections": ["mcp integration", "workflow automation", "tool orchestration"]
            }
        }
        
        self.results = {
            "passed": [],
            "failed": [],
            "warnings": []
        }

    def validate_file_existence(self) -> bool:
        """Validate that all expected files exist."""
        print("🔍 Validating file existence...")
        
        # Check master file
        if not self.master_file.exists():
            self.results["failed"].append(f"Master file missing: {self.master_file}")
            return False
        
        # Check instructions directory
        if not self.instructions_path.exists():
            self.results["failed"].append(f"Instructions directory missing: {self.instructions_path}")
            return False
        
        # Check all module files
        missing_files = []
        for module_file in self.expected_modules.keys():
            file_path = self.instructions_path / module_file
            if not file_path.exists():
                missing_files.append(str(file_path))
        
        if missing_files:
            self.results["failed"].extend([f"Missing module: {f}" for f in missing_files])
            return False
        
        self.results["passed"].append("✅ All required files exist")
        return True

    def validate_module_content(self) -> bool:
        """Validate content quality and structure of each module."""
        print("📝 Validating module content...")
        
        all_valid = True
        
        for module_file, properties in self.expected_modules.items():
            file_path = self.instructions_path / module_file
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check minimum lines
                line_count = len(content.split('\n'))
                if line_count < properties["min_lines"]:
                    self.results["failed"].append(
                        f"❌ {module_file}: Too short ({line_count} lines, expected ≥{properties['min_lines']})"
                    )
                    all_valid = False
                    continue
                
                # Check for key sections
                content_lower = content.lower()
                missing_sections = []
                for section in properties["key_sections"]:
                    if section.lower() not in content_lower:
                        missing_sections.append(section)
                
                if missing_sections:
                    self.results["warnings"].append(
                        f"⚠️ {module_file}: Missing sections: {', '.join(missing_sections)}"
                    )
                
                # Check for proper markdown structure
                if not content.startswith('#'):
                    self.results["failed"].append(f"❌ {module_file}: Missing main heading")
                    all_valid = False
                    continue
                
                # Check for trigger documentation
                if "**Triggers**:" not in content:
                    self.results["warnings"].append(f"⚠️ {module_file}: Missing trigger documentation")
                
                self.results["passed"].append(f"✅ {module_file}: Content validation passed")
                
            except Exception as e:
                self.results["failed"].append(f"❌ {module_file}: Error reading file: {e}")
                all_valid = False
        
        return all_valid

    def validate_master_integration(self) -> bool:
        """Validate that master file correctly references all modules."""
        print("🔗 Validating master file integration...")
        
        try:
            with open(self.master_file, 'r', encoding='utf-8') as f:
                master_content = f.read()
        except Exception as e:
            self.results["failed"].append(f"❌ Error reading master file: {e}")
            return False
        
        # Check for modular instruction system section
        if "MODULAR INSTRUCTION SYSTEM" not in master_content:
            self.results["failed"].append("❌ Master file missing MODULAR INSTRUCTION SYSTEM section")
            return False
        
        # Check that all modules are referenced
        missing_refs = []
        for module_file in self.expected_modules.keys():
            module_name = module_file.replace('.md', '')
            if module_name not in master_content:
                missing_refs.append(module_file)
        
        if missing_refs:
            self.results["failed"].extend([f"❌ Master file missing reference to: {ref}" for ref in missing_refs])
            return False
        
        # Check for activation triggers section
        if "activation_triggers:" not in master_content:
            self.results["failed"].append("❌ Master file missing activation triggers configuration")
            return False
        
        # Validate all triggers are documented
        for module_file, properties in self.expected_modules.items():
            module_name = module_file.replace('.md', '')
            trigger_pattern = f"{module_name}:"
            if trigger_pattern not in master_content:
                self.results["warnings"].append(f"⚠️ Master file missing triggers for: {module_name}")
        
        self.results["passed"].append("✅ Master file integration validated")
        return True

    def validate_trigger_consistency(self) -> bool:
        """Validate that triggers are consistent between master and modules."""
        print("🎯 Validating trigger consistency...")
        
        try:
            with open(self.master_file, 'r', encoding='utf-8') as f:
                master_content = f.read()
        except Exception as e:
            self.results["failed"].append(f"❌ Error reading master file: {e}")
            return False
        
        inconsistencies = []
        
        for module_file, properties in self.expected_modules.items():
            module_name = module_file.replace('.md', '')
            
            # Extract triggers from master file
            pattern = rf"{module_name}:\s*\"([^\"]+)\""
            match = re.search(pattern, master_content)
            
            if not match:
                inconsistencies.append(f"❌ No triggers found in master for: {module_name}")
                continue
            
            master_triggers = [t.strip() for t in match.group(1).split(',')]
            expected_triggers = properties["triggers"]
            
            # Check if triggers match
            missing_in_master = set(expected_triggers) - set(master_triggers)
            extra_in_master = set(master_triggers) - set(expected_triggers)
            
            if missing_in_master:
                inconsistencies.append(f"⚠️ {module_name}: Missing triggers in master: {missing_in_master}")
            
            if extra_in_master:
                inconsistencies.append(f"⚠️ {module_name}: Extra triggers in master: {extra_in_master}")
        
        if inconsistencies:
            self.results["warnings"].extend(inconsistencies)
        else:
            self.results["passed"].append("✅ Trigger consistency validated")
        
        return len(inconsistencies) == 0

    def validate_performance_claims(self) -> bool:
        """Validate performance optimization claims."""
        print("⚡ Validating performance optimization...")
        
        try:
            with open(self.master_file, 'r', encoding='utf-8') as f:
                master_content = f.read()
        except Exception as e:
            self.results["failed"].append(f"❌ Error reading master file: {e}")
            return False
        
        # Check for performance claims
        performance_indicators = [
            "85%+ context reduction",
            "≥9.5/10 quality",
            "≤1s assembly",
            "≥90% KV-cache hit rate"
        ]
        
        missing_claims = []
        for indicator in performance_indicators:
            if indicator not in master_content:
                missing_claims.append(indicator)
        
        if missing_claims:
            self.results["warnings"].extend([f"⚠️ Missing performance claim: {claim}" for claim in missing_claims])
        
        # Check for modular system configuration
        if "VIBECODE_V4_MODULAR_SYSTEM" not in master_content:
            self.results["failed"].append("❌ Missing VIBECODE V4 modular system configuration")
            return False
        
        self.results["passed"].append("✅ Performance optimization claims validated")
        return True

    def validate_feature_preservation(self) -> bool:
        """Validate that all original features are preserved."""
        print("🛡️ Validating feature preservation...")
        
        try:
            with open(self.master_file, 'r', encoding='utf-8') as f:
                master_content = f.read()
        except Exception as e:
            self.results["failed"].append(f"❌ Error reading master file: {e}")
            return False
        
        # Check for critical VIBECODE features
        critical_features = [
            "Memory Bank",
            "MCP Integration",
            "Context7",
            "VoidBeast",
            "BMad Method",
            "Quality Standards",
            "Research Protocols"
        ]
        
        missing_features = []
        for feature in critical_features:
            if feature not in master_content:
                missing_features.append(feature)
        
        if missing_features:
            self.results["failed"].extend([f"❌ Missing critical feature: {feature}" for feature in missing_features])
            return False
        
        self.results["passed"].append("✅ Feature preservation validated")
        return True

    def generate_report(self) -> Dict:
        """Generate comprehensive validation report."""
        print("\n" + "="*50)
        print("📊 VALIDATION REPORT")
        print("="*50)
        
        # Count results
        passed_count = len(self.results["passed"])
        failed_count = len(self.results["failed"])
        warning_count = len(self.results["warnings"])
        
        print(f"✅ Passed: {passed_count}")
        print(f"❌ Failed: {failed_count}")
        print(f"⚠️ Warnings: {warning_count}")
        print()
        
        # Show details
        if self.results["passed"]:
            print("✅ PASSED VALIDATIONS:")
            for item in self.results["passed"]:
                print(f"  {item}")
            print()
        
        if self.results["warnings"]:
            print("⚠️ WARNINGS:")
            for item in self.results["warnings"]:
                print(f"  {item}")
            print()
        
        if self.results["failed"]:
            print("❌ FAILED VALIDATIONS:")
            for item in self.results["failed"]:
                print(f"  {item}")
            print()
        
        # Overall status
        overall_status = "PASS" if failed_count == 0 else "FAIL"
        print(f"🎯 OVERALL STATUS: {overall_status}")
        
        if overall_status == "PASS":
            print("🎉 VIBECODE V4.0 Modular Architecture is fully validated!")
        else:
            print("🔧 Issues found - please review and fix before proceeding.")
        
        return {
            "status": overall_status,
            "passed": passed_count,
            "failed": failed_count,
            "warnings": warning_count,
            "details": self.results
        }

    def run_full_validation(self) -> Dict:
        """Run complete validation suite."""
        print("🚀 Starting VIBECODE V4.0 Modular Architecture Validation")
        print("="*60)
        
        validation_steps = [
            ("File Existence", self.validate_file_existence),
            ("Module Content", self.validate_module_content),
            ("Master Integration", self.validate_master_integration),
            ("Trigger Consistency", self.validate_trigger_consistency),
            ("Performance Claims", self.validate_performance_claims),
            ("Feature Preservation", self.validate_feature_preservation)
        ]
        
        for step_name, step_func in validation_steps:
            print(f"\n🔍 {step_name}...")
            try:
                step_func()
            except Exception as e:
                self.results["failed"].append(f"❌ {step_name}: Validation error: {e}")
        
        return self.generate_report()

def main():
    """Main execution function."""
    # Change to the correct directory if needed
    if os.path.exists(".github"):
        base_path = ".github"
    elif os.path.exists("../.github"):
        base_path = "../.github"
    else:
        print("❌ .github directory not found. Please run from project root.")
        return
    
    validator = ModularArchitectureValidator(base_path)
    result = validator.run_full_validation()
    
    # Exit with appropriate code
    exit_code = 0 if result["status"] == "PASS" else 1
    exit(exit_code)

if __name__ == "__main__":
    main()
