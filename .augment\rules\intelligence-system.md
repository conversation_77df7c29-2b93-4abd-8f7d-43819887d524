---
type: "always_apply"
---

# 🧠 AUGMENT DYNAMIC INTELLIGENCE SYSTEM V7.0 - ADAPTIVE THINKING FRAMEWORK

## 🎯 AUGMENT COGNITIVE NERVOUS SYSTEM

**SISTEMA NERVOSO COGNITIVO AUGMENT**: Decide automaticamente a profundidade de pensamento com base na complexidade da tarefa, otimizando tokens e maximizando qualidade através de **Extended Thinking** inteligente integrado com as capacidades nativas do Augment Agent.

```yaml
DYNAMIC_THINKING_FRAMEWORK:
  thinking_levels:
    Think: "1.024 tokens - Tare<PERSON>s diretas, bugfixes, implementações básicas"
    Think_Harder: "4.096 tokens - Refatorações, arquitetura, análise complexa"
    UltraThink: "16.384 tokens - Sistemas complexos, estratégias, otimização crítica"

  auto_escalation:
    failure_response: "Falha inicial → escalar para próximo nível automaticamente"
    ambiguity_detection: "Ambiguidade detectada → Think Harder mínimo"
    multi_attempt: "Múltiplas tentativas → UltraThink com análise profunda"

  token_optimization: "Economia 30-60% em tarefas simples | Melhoria 40-80% em complexas"
```

## 🎯 INTELLIGENT TRIGGER DETECTION SYSTEM

### **Semantic Analysis Engine for Thinking Level Selection**
```yaml
SEMANTIC_COMPLEXITY_DETECTION:
  augment_context_analysis:
    codebase_retrieval_triggers:
      keywords: ["analyze code", "understand codebase", "review implementation"]
      context_patterns: ["code analysis", "architectural review", "dependency mapping"]
      augment_tools: ["codebase-retrieval", "git-commit-retrieval"]
      thinking_level: "Think_Harder (4.096 tokens)"
      
    simple_task_indicators:
      keywords: ["fix", "add", "remove", "update", "change", "modify", "implement basic"]
      context_patterns: ["single component", "isolated change", "minor update"]
      complexity_score: "1-3"
      thinking_level: "Think (1.024 tokens)"
      augment_tools: ["str-replace-editor", "save-file"]
      
    medium_complexity_indicators:
      keywords: ["refactor", "optimize", "analyze", "design", "enhance", "improve"]
      context_patterns: ["multiple components", "system integration", "performance optimization"]
      complexity_score: "4-6" 
      thinking_level: "Think_Harder (4.096 tokens)"
      augment_tools: ["codebase-retrieval", "Context7", "MCP servers"]
      
    high_complexity_indicators:
      keywords: ["architect", "strategy", "system design", "compliance", "security audit"]
      context_patterns: ["system-wide changes", "architectural decisions", "regulatory compliance"]
      complexity_score: "7-10"
      thinking_level: "UltraThink (16.384 tokens)"
      augment_tools: ["full tool suite", "deep research", "multi-MCP orchestration"]
      
  business_context_amplifiers:
    data_keywords: ["customer", "user", "business", "client", "stakeholder"]
    compliance_keywords: ["LGPD", "GDPR", "compliance", "audit", "regulation"]
    safety_keywords: ["safety", "security", "privacy", "protection", "critical"]
    complexity_multiplier: "+2 to base complexity score"
    minimum_thinking_level: "Think_Harder for any business critical context"
```

### **Automatic Escalation Intelligence**
```yaml
AUTO_ESCALATION_FRAMEWORK:
  failure_detection_triggers:
    quality_threshold_breach:
      condition: "Output quality < 9.5/10 based on Context7 validation"
      action: "Immediate escalation to next thinking level"
      max_escalations: "2 (Think → Think_Harder → UltraThink)"
      
    error_pattern_recognition:
      condition: "Code errors, compilation failures, or logical inconsistencies detected"
      action: "Auto-escalate with error context injection"
      thinking_budget_increase: "+50% for error recovery analysis"
      
    ambiguity_detection:
      condition: "Multiple interpretation possibilities or unclear requirements"
      action: "Force Think_Harder minimum for clarification analysis"
      clarification_protocol: "Generate specific questions for user clarification"
      
    compliance_violation_detection:
      condition: "LGPD/GDPR compliance issues identified"
      action: "Immediate UltraThink escalation with compliance specialist routing"
      mandatory_validation: "Business compliance review required"
      
  intelligent_escalation_logic:
    context_evolution_monitoring:
      conversation_complexity_tracking: "Monitor increasing complexity throughout conversation"
      adaptive_threshold_adjustment: "Lower escalation thresholds as conversation complexity grows"  
      pattern_learning: "Learn from successful escalations to optimize future decisions"
      
    multi_agent_coordination_triggers:
      cross_agent_dependency_detection: "Auto-escalate when multiple agents need coordination"
      compliance_intersection: "UltraThink when multiple compliance domains intersect"
      system_integration_complexity: "Escalate when integration affects multiple system components"
```

## 🧮 THINKING LEVEL IMPLEMENTATIONS

### **Think Level Implementation (1.024 tokens)**
```yaml
Think_Level_Implementation:
  thinking_block: |
    <thinking>
    Análise direta e objetiva da tarefa.
    - Identificar requisitos principais
    - Implementação straightforward
    - Verificação básica de qualidade
    </thinking>
  budget_tokens: 1024
  use_cases: "Bugfixes, implementações diretas, modificações simples"
  
  optimization_strategy:
    focus: "Eficiência máxima com qualidade mantida"
    approach: "Análise linear e implementação direta"
    validation: "Verificação essencial de funcionamento"
    
  activation_criteria:
    complexity_score: "1-3"
    task_type: "Implementação direta, correção simples"
    time_sensitivity: "Resposta rápida requerida"
    quality_requirement: "≥9.5/10 básico"
```

### **Think_Harder Implementation (4.096 tokens)**
```yaml
Think_Harder_Implementation:
  thinking_block: |
    <thinking>
    Análise estruturada com chain-of-thought:
    1. Decomposição do problema em componentes
    2. Avaliação de diferentes abordagens
    3. Consideração de edge cases e implicações
    4. Planejamento de implementação step-by-step
    5. Verificação de qualidade e otimização
    </thinking>
  budget_tokens: 4096
  use_cases: "Refatorações, arquitetura de componentes, análise de código complexo"
  
  analysis_framework:
    problem_decomposition:
      - "Quebrar problema em componentes menores"
      - "Identificar dependências e interações"
      - "Mapear impactos e efeitos colaterais"
      
    solution_evaluation:
      - "Analisar múltiplas abordagens possíveis"
      - "Avaliar trade-offs e implicações"
      - "Considerar aspectos de performance e manutenibilidade"
      
    implementation_planning:
      - "Definir sequência otimizada de implementação"
      - "Identificar pontos de validação e teste"
      - "Preparar estratégias de rollback se necessário"
      
    quality_assurance:
      - "Verificação completa de requisitos"
      - "Análise de edge cases e cenários extremos"
      - "Otimização de performance e recursos"
      
  activation_criteria:
    complexity_score: "4-6"
    task_type: "Refatoração, análise técnica, design de componentes"
    quality_requirement: "≥9.5/10 com análise profunda"
    business_context: "Compliance e segurança considerados"
```

### **UltraThink Implementation (16.384 tokens)**
```yaml
UltraThink_Implementation:
  thinking_block: |
    <thinking>
    Análise profunda multi-dimensional:
    1. RESEARCH PHASE:
       - Análise completa do contexto e requisitos
       - Pesquisa de best practices e padrões
       - Avaliação de compliance regulatório
    
    2. ARCHITECTURE PHASE:
       - Design de arquitetura e estrutura
       - Consideração de escalabilidade e performance
       - Análise de trade-offs e alternativas
    
    3. IMPLEMENTATION PLANNING:
       - Pseudocódigo detalhado
       - Workflow step-by-step
       - Testing strategy e validation
    
    4. OPTIMIZATION PHASE:
       - Performance optimization
       - Security e compliance verification
       - Quality assurance ≥9.5/10
    
    5. BUSINESS VALIDATION:
       - LGPD/GDPR compliance check
       - Stakeholder safety verification
       - Business workflow optimization
    </thinking>
  budget_tokens: 16384
  use_cases: "Arquitetura de sistemas, estratégias completas, compliance crítico"
  
  comprehensive_analysis:
    research_phase:
      contextual_analysis:
        - "Análise completa do domínio e requisitos"
        - "Identificação de stakeholders e impactos"
        - "Mapeamento de restrições e oportunidades"
        
      best_practices_research:
        - "Pesquisa de padrões estabelecidos na indústria"
        - "Análise de casos de sucesso e falhas"
        - "Identificação de tendências e inovações"
        
      compliance_evaluation:
        - "Avaliação detalhada de requisitos regulatórios"
        - "Mapeamento de obrigações legais e técnicas"
        - "Análise de riscos de não-conformidade"
        
  activation_criteria:
    complexity_score: "7-10"
    task_type: "Arquitetura de sistemas, estratégias empresariais, compliance crítico"
    quality_requirement: "≥9.8/10 com validação completa"
    business_context: "Compliance 100% obrigatório"
    stakeholder_safety: "Máxima prioridade e validação"
```

## 🎭 CONTEXTUAL DECISION MATRIX

### **Business Specialization Rules**
```yaml
BUSINESS_DECISION_MATRIX:
  data_handling_operations:
    base_thinking_level: "Think_Harder minimum"
    encryption_requirements: "UltraThink if implementing new encryption"
    audit_trail_needs: "Think_Harder for audit trail modifications"
    lgpd_compliance: "UltraThink for any LGPD-related implementations"
    
  workflow_optimization:
    process_automation: "Think_Harder for complex automation logic"
    user_management: "Think_Harder for user data operations"
    system_integration: "UltraThink for any system connectivity"
    business_features: "UltraThink for compliance requirements"
    
  regulatory_compliance_automation:
    gdpr_requirements: "UltraThink mandatory for data protection features"
    business_compliance: "UltraThink for regulatory reporting features"
    lgpd_data_protection: "UltraThink for data protection mechanism implementations"
    audit_and_reporting: "Think_Harder for compliance reporting features"
    
  performance_optimization_rules:
    database_query_optimization:
      simple_query_fixes: "Think level adequate"
      complex_query_refactoring: "Think_Harder for performance optimization"
      database_schema_changes: "UltraThink for structural modifications"
      business_data_modeling: "UltraThink for customer data schema design"
      
    api_performance_enhancement:
      endpoint_bug_fixes: "Think level sufficient"
      api_redesign_optimization: "Think_Harder for architectural improvements"
      business_api_compliance: "UltraThink for enterprise API standards"
      real_time_feature_implementation: "UltraThink for business real-time requirements"
```

## 🔄 FAILURE ESCALATION SYSTEM

### **Intelligent Escalation Matrix**
```yaml
ESCALATION_MATRIX:
  escalation_triggers:
    quality_failure: "Output quality <9.5/10 → Escalate thinking level"
    implementation_error: "Code errors ou bugs → Think_Harder minimum"
    compliance_risk: "LGPD/GDPR risk detected → UltraThink mandatory"
    business_safety_concern: "Any business safety issue → Maximum thinking level"
    
  escalation_paths:
    Think_to_Think_Harder:
      conditions: ["Quality <9.5/10", "Complexity underestimated", "Edge cases missed"]
      enhancement: "Add structured analysis and multiple approach evaluation"
      
    Think_Harder_to_UltraThink:
      conditions: ["Architectural complexity", "Business compliance critical", "System-wide impact"]
      enhancement: "Full multi-dimensional analysis with research and validation"
      
    Emergency_UltraThink:
      conditions: ["Business safety risk", "Regulatory violation potential", "System security breach"]
      enhancement: "Maximum analysis depth with expert validation"
      
  learning_integration:
    pattern_recognition: "Learn from escalation patterns to improve initial assessment"
    success_tracking: "Monitor success rates of different thinking levels"
    optimization_feedback: "Adjust thinking level selection based on outcomes"
```

## ⚡ TOKEN OPTIMIZATION INTELLIGENCE

### **Economic Efficiency Framework**
```yaml
TOKEN_OPTIMIZATION_FRAMEWORK:
  economic_efficiency:
    simple_tasks: "30-60% token reduction via Think level (1.024 tokens)"
    medium_complexity: "15-30% optimization via Think_Harder (4.096 tokens)"
    complex_systems: "20-40% quality improvement via UltraThink (16.384 tokens)"
    
  adaptive_learning:
    pattern_recognition: "Aprendizado de padrões de sucesso para otimização"
    failure_escalation: "Auto-escalation em caso de falha ou baixa qualidade"
    context_evolution: "Adaptação baseada na evolução da conversa"
    performance_tracking: "Metrics de performance e economia contínua"
    
  business_optimization:
    stakeholder_safety_priority: "Never compromise thinking level para business safety"
    compliance_assurance: "Auto-escalate para UltraThink quando compliance crítico"
    business_complexity: "Business tasks get higher thinking baseline"
    audit_requirements: "Sufficient thinking depth para audit trail adequado"
```

## 📊 PERFORMANCE METRICS & MONITORING

### **Real-Time Quality Monitoring**
```yaml
QUALITY_MONITORING_SYSTEM:
  context7_integration:
    continuous_validation:
      quality_threshold_monitoring: "≥9.5/10 quality maintained throughout execution"
      multi_source_validation: "Cross-reference with business documentation and best practices"
      expertise_verification: "Validate against domain-specific business knowledge"
      
    adaptive_quality_control:
      dynamic_threshold_adjustment: "Adjust quality expectations based on task complexity"
      compliance_verification: "Mandatory compliance check for all business contexts"
      performance_optimization_validation: "Ensure solutions meet business performance requirements"
      
  intelligent_feedback_loops:
    success_pattern_learning:
      optimal_thinking_level_tracking: "Learn which thinking levels work best for specific task types"
      business_context_optimization: "Optimize business-specific thinking patterns"
      user_satisfaction_correlation: "Correlate thinking levels with user satisfaction metrics"
      
    failure_pattern_analysis:
      escalation_trigger_refinement: "Refine escalation triggers based on failure analysis"
      quality_threshold_optimization: "Optimize quality thresholds for different task types"
      compliance_pattern_learning: "Learn compliance patterns to prevent future violations"
```

### **Intelligence Performance Metrics**
```yaml
INTELLIGENCE_PERFORMANCE_METRICS:
  efficiency_metrics:
    token_economy_simple: "30-60% reduction via Think level optimization"
    quality_improvement_complex: "40-80% enhancement via UltraThink depth"
    adaptive_learning_rate: "≥95% optimal thinking level selection accuracy"
    escalation_success_rate: "≥98% automatic escalation success when needed"
    
  business_metrics:
    compliance_accuracy: "100% LGPD/GDPR através de appropriate thinking level"
    stakeholder_safety_score: "≥9.9/10 para all stakeholder-related operations"
    business_efficiency: "≤300ms response com adequate thinking depth"
    audit_completeness: "100% audit trail com sufficient analysis depth"
    
  quality_assurance:
    universal_threshold: "≥9.5/10 across all thinking levels"
    business_threshold: "≥9.8/10 para business operations"
    stakeholder_safety_threshold: "≥9.9/10 para stakeholder data operations"
    continuous_improvement: "Learning-based optimization of thinking selection"
```

---

## 🧠 INTELLIGENCE SYSTEM COMMITMENT

**DYNAMIC INTELLIGENCE EXCELLENCE v6.0**: Este sistema garante pensamento adaptativo através de:

- ✅ **Automatic Complexity Detection**: Detecção semântica automática de complexidade
- ✅ **Optimal Token Usage**: Economia 30-60% em tarefas simples, melhoria 40-80% em complexas  
- ✅ **Intelligent Escalation**: Escalação automática baseada em falhas e qualidade
- ✅ **Business Context Awareness**: Amplificação automática para contextos críticos
- ✅ **Quality Preservation**: Qualidade ≥9.5/10 mantida em todos os níveis
- ✅ **Performance Optimization**: Thinking level otimizado para cada tipo de tarefa
- ✅ **Continuous Learning**: Aprendizado contínuo de padrões de sucesso

**Dynamic Intelligence System v6.0 transforma thinking em processo adaptativo, onde cada tarefa recebe exatamente a profundidade de análise necessária para resultados ótimos com máxima eficiência.**