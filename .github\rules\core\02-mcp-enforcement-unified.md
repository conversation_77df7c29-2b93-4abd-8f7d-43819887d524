---
alwaysApply: true
description: 'MCP ENFORCEMENT UNIFIED - Complete MCP System Authority & Intelligence'
version: '6.0'
title: 'MCP Enforcement Unified - Supreme MCP Control & Intelligent Routing'
type: 'mcp-enforcement-unified'
mcp_servers: ['desktop-commander', 'sequential-thinking', 'context7-mcp', 'tavily-mcp', 'exa-mcp']
quality_threshold: 9.7
specialization: 'mcp-unified-enforcement-intelligence'
trigger_keywords: ['mcp', 'routing', 'desktop-commander', 'file', 'terminal', 'command', 'optimization', 'context', 'engineering', 'intelligence']
enforcement_level: 'absolute'
approach: 'mcp-mandatory-intelligent-unified'
globs: '**/*'
priority: 1
---

# 🔧 MCP ENFORCEMENT UNIFIED - Supreme MCP Authority & Intelligent Routing

## 🚨 ABSOLUTE MCP REQUIREMENTS - NON-NEGOTIABLE

### **Supreme MCP Usage Enforcement (Production Enhanced 2025)**

```yaml
ABSOLUTE_MCP_REQUIREMENTS_2025:
  NO_TASK_WITHOUT_MCP: "Every task must use appropriate MCPs - NO EXCEPTIONS - PRODUCTION RELIABILITY"
  NO_BYPASSING_SEQUENTIAL: "Sequential Thinking MCP required for complexity ≥3 with structured output"
  NO_RESEARCH_WITHOUT_VALIDATION: "All research must use Context7 + Tavily + Exa + CROSS-VALIDATION"
  NO_FILES_WITHOUT_COMMANDER: "All file operations must use Desktop Commander with atomic transactions"
  VALIDATE_MCP_STATUS: "Check MCP functionality AND PERFORMANCE METRICS before task execution"
  PRODUCTION_FIRST_ENFORCEMENT: "All MCP usage must include production reliability validation"
  
12_FACTOR_MCP_INTEGRATION:
  stateless_operations: "Each MCP call is independent and atomic"
  context_ownership: "Explicit context window management per MCP"
  structured_output: "Natural language to JSON/structured format conversion"
  execution_separation: "LLM reasoning separate from MCP execution"
  
CORE_MCP_PRINCIPLES:
  "MCP-First mandatory approach for ALL system operations"
  "Intelligent routing with optimized MCP chains based on complexity"
  "Zero direct system access - 100% MCP enforcement with safety validation"
  "Desktop Commander as supreme file/terminal authority with transaction safety"
  "Production-grade reliability with comprehensive safety validation"
```

### **Enhanced MCP Routing Logic (Safety-Integrated)**

```yaml
ROUTING_LOGIC_SAFETY_ENHANCED:
  complexity_1_3: 
    minimum_mcps: "Sequential Thinking + Desktop Commander"
    safety_validation: "Basic input validation + output safety check"
    use_cases: ["Basic code completion", "Simple syntax questions", "Quick documentation lookup"]
    
  complexity_4_6: 
    minimum_mcps: "Sequential + Context7 + Desktop Commander + 1 research MCP"
    safety_validation: "Enhanced bias detection + security validation"
    optimization: "70%+ token reduction through strategic MCP selection"
    use_cases: ["Multi-step processes", "Basic integration", "Technology-specific implementations"]
    
  complexity_7_10: 
    minimum_mcps: "ALL 5 MCP servers must be used"
    safety_validation: "Full red-teaming + comprehensive safety validation + expert review"
    optimization: "Quality-first approach with full MCP orchestration"
    use_cases: ["System integration", "Architectural decisions", "Multi-domain coordination"]
    
  research_tasks: 
    minimum_mcps: "ALL 5 MCP servers regardless of complexity"
    safety_validation: "Multi-source safety validation + bias checking + responsible AI compliance"
    optimization: "Comprehensive research with cross-validation"
    use_cases: ["Complex research", "Multi-source analysis", "Expert-level investigation"]
```

## 🧠 INTELLIGENT MCP ROUTING & COMPLEXITY ASSESSMENT

### **APEX Complexity Detection Engine**

```yaml
COMPLEXITY_DETECTION_ENGINE_APEX:
  base_score: 1.0
  
  cognitive_load_analysis:
    L1_simple: 
      multiplier: "1.0x"
      characteristics: ["Direct implementation", "Simple queries", "Basic operations"]
      keywords: ["read", "view", "list", "show", "get", "find"]
      
    L2_moderate:
      multiplier: "1.5x" 
      characteristics: ["Multi-step processes", "Basic integration", "Moderate analysis"]
      keywords: ["modify", "update", "fix", "debug", "optimize", "refactor"]
      
    L3_complex:
      multiplier: "2.5x"
      characteristics: ["System integration", "Architectural decisions", "Complex analysis"]
      keywords: ["implement", "create", "build", "develop", "integrate"]
      
    L4_enterprise:
      multiplier: "4.0x"
      characteristics: ["Multi-domain coordination", "Critical systems", "Enterprise architecture"]
      keywords: ["architecture", "system", "design", "orchestrate", "scale"]
  
  technical_depth_assessment:
    surface_level:
      multiplier: "1.0x"
      characteristics: ["Basic operations", "Simple queries", "UI/Frontend focus"]
      indicators: ["ui", "frontend", "styling", "layout"]
      
    intermediate:
      multiplier: "1.5x"
      characteristics: ["Technology-specific implementations", "API integration"]
      indicators: ["api", "backend", "database", "logic"]
      
    deep_technical:
      multiplier: "2.0x"
      characteristics: ["Complex integrations", "Performance optimization", "Security"]
      indicators: ["security", "performance", "scalability", "infrastructure"]
      
    expert_level:
      multiplier: "3.0x"
      characteristics: ["Architecture design", "System-wide optimization", "Enterprise patterns"]
      indicators: ["microservices", "distributed", "enterprise", "optimization"]
  
  integration_scope_analysis:
    single_component:
      multiplier: "1.0x"
      characteristics: ["Isolated operations", "Single file/component changes"]
      
    module_integration:
      multiplier: "1.5x"
      characteristics: ["Cross-module coordination", "Multiple file changes"]
      
    system_integration:
      multiplier: "2.0x"
      characteristics: ["Multi-system coordination", "API integrations"]
      
    enterprise_coordination:
      multiplier: "2.5x"
      characteristics: ["Organization-wide impacts", "Complex system orchestration"]
  
  risk_assessment_factors:
    low_risk:
      multiplier: "1.0x"
      characteristics: ["Safe operations", "Reversible changes", "Documentation updates"]
      
    medium_risk:
      multiplier: "1.2x"
      characteristics: ["Moderate impact", "Careful consideration required"]
      
    high_risk:
      multiplier: "1.5x"
      characteristics: ["High impact", "Extensive validation required"]
      indicators: ["breaking changes", "refactoring", "database"]
      
    critical_risk:
      multiplier: "2.0x"
      characteristics: ["Mission-critical", "Zero-tolerance for errors"]
      indicators: ["security", "data integrity", "production", "migration"]
```

### **Intelligent MCP Chain Selection**

```yaml
MCP_CHAIN_OPTIMIZATION_MATRIX:
  research_workflows:
    basic_documentation:
      chain: "context7 → desktop-commander"
      complexity: "1-3"
      optimization: "85% token reduction"
      
    current_information:
      chain: "tavily → context7 → desktop-commander"
      complexity: "3-5"
      optimization: "70% token reduction"
      
    comprehensive_research:
      chain: "exa → tavily → context7 → sequential-thinking → desktop-commander"
      complexity: "6-10"
      optimization: "Quality-first approach"
      
  implementation_workflows:
    guided_development:
      chain: "context7 → sequential-thinking → desktop-commander"
      complexity: "2-4"
      optimization: "75% token reduction"
      
    complex_implementation:
      chain: "tavily → context7 → sequential-thinking → desktop-commander"
      complexity: "5-7"
      optimization: "60% token reduction"
      
    enterprise_development:
      chain: "exa → tavily → context7 → sequential-thinking → desktop-commander"
      complexity: "8-10"
      optimization: "Quality and reliability first"
      
  analysis_workflows:
    file_analysis:
      chain: "desktop-commander → sequential-thinking"
      complexity: "1-3"
      optimization: "90% token reduction"
      
    research_analysis:
      chain: "exa → tavily → sequential-thinking → desktop-commander"
      complexity: "4-8"
      optimization: "Comprehensive analysis focus"
      
    system_analysis:
      chain: "exa → tavily → context7 → sequential-thinking → desktop-commander"
      complexity: "7-10"
      optimization: "Deep system understanding"
```

## 🛡️ DESKTOP COMMANDER ABSOLUTE ENFORCEMENT

### **Mandatory File Operation Protocol (Zero Tolerance)**

```yaml
DESKTOP_COMMANDER_ABSOLUTE_PROTOCOL:
  mandatory_sequence:
    step_1_directory_verification:
      action: "✅ ALWAYS check target directory exists using mcp_desktop-comma_list_directory"
      enforcement: "❌ NO FILE OPERATIONS without directory verification - ZERO TOLERANCE"
      validation: "Directory path existence and permissions verification"
      
    step_2_directory_creation:
      action: "✅ ALWAYS create directory if missing using mcp_desktop-comma_create_directory"
      enforcement: "❌ NO ASSUMPTIONS about directory existence - ALWAYS VERIFY"
      validation: "Directory creation success confirmation"
      
    step_3_creation_validation:
      action: "✅ ALWAYS verify directory creation success with mcp_desktop-comma_list_directory"
      enforcement: "❌ NO PROCEEDING without confirmed directory existence"
      validation: "Post-creation directory existence validation"
      
    step_4_file_operation:
      action: "✅ ONLY THEN proceed with file operations using mcp_desktop-comma_write_file"
      enforcement: "❌ NO FILE WRITES without directory protocol completion"
      validation: "File operation execution with transaction safety"
      
    step_5_operation_validation:
      action: "✅ ALWAYS verify file operation success and validate content"
      enforcement: "❌ NO TASK COMPLETION without operation validation"
      validation: "File content verification and operation success confirmation"

ZERO_TOLERANCE_ENFORCEMENT:
  absolute_prohibitions:
    - "❌ NO FILE WRITE without directory verification - ZERO TOLERANCE"
    - "❌ NO ASSUMPTIONS about directory existence - ALWAYS VERIFY"
    - "❌ NO SKIPPING directory creation step - MANDATORY PROTOCOL"
    - "❌ NO BYPASSING desktop commander for ANY file/terminal operation"
    - "❌ NO DIRECT SYSTEM ACCESS - MCP mandatory for all operations"
    
  mandatory_tools:
    file_operations:
      read: "mcp_desktop-comma_read_file"
      write: "mcp_desktop-comma_write_file"
      list: "mcp_desktop-comma_list_directory"
      create_dir: "mcp_desktop-comma_create_directory"
      search: "mcp_desktop-comma_search_files / mcp_desktop-comma_search_code"
      
    terminal_operations:
      execute: "mcp_desktop-comma_start_process"
      interact: "mcp_desktop-comma_interact_with_process"
      monitor: "mcp_desktop-comma_list_processes"
      terminate: "mcp_desktop-comma_kill_process"
```

### **Prohibited Tools (Absolute Ban)**

```yaml
PROHIBITED_TOOLS_ABSOLUTE:
  never_use_ever:
    file_operations:
      - "❌ NEVER: Direct file system access (fs, open, read, write, etc.)"
      - "❌ NEVER: Built-in file operations without MCP"
      - "❌ NEVER: Python file I/O directly (use desktop commander)"
      - "❌ NEVER: Any file operation without desktop-commander MCP"
      
    terminal_operations:
      - "❌ NEVER: run_command tool (FORBIDDEN - use desktop-commander MCP)"
      - "❌ NEVER: PowerShell direct execution (FORBIDDEN - use desktop-commander MCP)"
      - "❌ NEVER: Shell commands direct (FORBIDDEN - use desktop-commander MCP)"
      - "❌ NEVER: subprocess calls (FORBIDDEN - use desktop-commander MCP)"
      - "❌ NEVER: os.system calls (FORBIDDEN - use desktop-commander MCP)"
    
    system_operations:
      - "❌ NEVER: Direct system calls without MCP validation"
      - "❌ NEVER: Bypass MCP for any system operation"
      - "❌ NEVER: Native command execution without MCP orchestration"
      
  violation_consequences:
    immediate_actions:
      - "❌ IMMEDIATE STOP of current operation"
      - "❌ FORCE CORRECTION to appropriate MCP tool"
      - "❌ LOG VIOLATION for pattern analysis and prevention"
      - "❌ REQUIRE EXPLICIT JUSTIFICATION for any deviation"
      - "❌ SYSTEM-WIDE ENFORCEMENT ALERT and escalation"
```

## 🚨 MANDATORY CONVERSATION INITIALIZATION

### **MCP Validation Checkpoint (Every Conversation)**

```yaml
MCP_STATUS_CHECK_MANDATORY:
  pre_task_validation:
    sequential_thinking: "✅ Test with simple reasoning task and validate response"
    desktop_commander: "✅ Test with file read operation and confirm functionality"  
    context7: "✅ Test with library resolution and validate documentation access"
    tavily: "✅ Test with simple search and confirm current information access"
    exa: "✅ Test with neural search and validate semantic search capability"
    
  workflow_validation:
    vibecode_workflow: "✅ Confirm 5-step workflow sequence ready for execution"
    quality_threshold: "✅ Verify ≥9.7/10 requirement active and enforced"
    confidence_minimum: "✅ Verify ≥95% confidence requirement active"
    safety_protocols: "✅ Confirm safety validation and responsible AI protocols active"
    
  integration_check:
    mcp_chain_ready: "✅ All MCPs functional, integrated, and performance validated"
    intelligent_routing: "✅ Complexity detection and routing algorithms active"
    desktop_commander_ready: "✅ File operation protocols active with zero tolerance enforcement"
    quality_gates_armed: "✅ Quality validation checkpoints active and operational"
```

## 🔄 MANDATORY TASK TRANSITION PROTOCOL

### **Task Transition Enforcement (Critical)**

```yaml
TASK_TRANSITION_PROTOCOL:
  transition_triggers: ["próxima task", "next task", "siga para", "continue", "implementar", "desenvolver"]
  
  mandatory_sequence:
    step_1_research_validation:
      action: "Context7 MCP - Research latest official documentation and best practices"
      enforcement: "❌ NO IMPLEMENTATION without current documentation validation"
      
    step_2_current_trends:
      action: "Tavily MCP - Research current community discussions and implementation patterns"
      enforcement: "❌ NO PROCEEDING without current trend validation"
      
    step_3_expert_analysis:
      action: "Exa MCP - Research expert articles and advanced techniques"
      enforcement: "❌ NO IMPLEMENTATION without expert pattern validation"
      
    step_4_synthesis:
      action: "Sequential Thinking MCP - Synthesize findings into actionable improvements"
      enforcement: "❌ NO EXECUTION without comprehensive synthesis"
      
    step_5_implementation:
      action: "Desktop Commander MCP - Apply research insights to optimize task execution"
      enforcement: "❌ NO COMPLETION without MCP-backed implementation"
      
  continuous_improvement_mandate:
    during_implementation:
      - "✅ ALWAYS research better patterns while coding using Context7 documentation"
      - "✅ ALWAYS check for performance optimizations via Context7 guidelines"
      - "✅ ALWAYS validate against latest best practices using Context7 real-time docs"
      - "✅ ALWAYS apply security best practices validated through Context7"
      - "✅ ALWAYS validate API usage patterns against Context7 current documentation"
      
    persistence_enforcement:
      - "Continue improving until no further enhancements possible"
      - "Keep optimizing until ≥9.7/10 quality consistently achieved"
      - "Continue researching until comprehensive understanding achieved"
      - "Keep refining until implementation is perfect and production-ready"
```

## 📊 MCP PERFORMANCE & MONITORING

### **Performance Standards & Quality Gates**

```yaml
MCP_PERFORMANCE_STANDARDS:
  operation_speed:
    desktop_commander_ops: "<5s for standard file operations with transaction safety"
    context7_lookups: "<10s for documentation retrieval with caching optimization"
    tavily_searches: "<15s for current information searches with result validation"
    exa_searches: "<20s for semantic research operations with quality filtering"
    sequential_thinking: "<30s for complex analysis tasks with structured output"
    
  chain_efficiency:
    simple_chains: "<30s total execution (desktop-commander + sequential)"
    moderate_chains: "<60s total execution (context7 + sequential + desktop)"
    complex_chains: "<120s total execution (tavily + context7 + sequential + desktop)"
    comprehensive_chains: "<300s total execution (full 5-MCP orchestration)"
    
  quality_consistency:
    mcp_success_rate: "≥98% successful MCP operations with error handling"
    chain_completion_rate: "≥95% successful chain completions with validation"
    quality_threshold_achievement: "100% ≥9.7/10 quality maintenance"
    error_recovery_rate: "≥99% successful error recovery with graceful degradation"
    
  safety_validation:
    bias_detection_rate: "≥95% accuracy in bias detection and mitigation"
    security_validation: "100% security validation for all operations"
    responsible_ai_compliance: "100% compliance with responsible AI guidelines"
    safety_intervention_rate: "≥99% successful safety interventions when needed"
```

### **Intelligent Monitoring & Optimization**

```yaml
INTELLIGENT_MCP_MONITORING:
  real_time_metrics:
    complexity_prediction_accuracy: "≥95% accuracy in complexity assessment"
    routing_optimization_effectiveness: "≥90% optimal MCP chain selection"
    performance_target_achievement: "≥85% operations meeting performance targets"
    quality_threshold_maintenance: "100% operations meeting ≥9.7/10 quality"
    
  adaptive_optimization:
    dynamic_chain_selection: "Real-time adaptation based on task characteristics"
    performance_tuning: "Continuous optimization of MCP parameters"
    cache_optimization: "Dynamic caching strategies for improved performance"
    load_balancing: "Intelligent load distribution across MCP servers"
    
  continuous_learning:
    pattern_recognition: "Learn successful MCP usage patterns for replication"
    failure_analysis: "Analyze and prevent recurring MCP operation failures"
    optimization_opportunities: "Identify and implement performance improvements"
    quality_enhancement: "Continuous improvement of quality achievement rates"
```

## 🚨 ENFORCEMENT & COMPLIANCE VALIDATION

### **Zero Tolerance Compliance System**

```yaml
ZERO_TOLERANCE_COMPLIANCE:
  real_time_monitoring:
    operation_interception: "Intercept ALL file/terminal operations before execution"
    mcp_compliance_validation: "Validate 100% MCP compliance for all operations"
    automatic_conversion: "Convert non-MCP operations to MCP equivalents"
    violation_blocking: "Block any non-MCP operation attempts with immediate correction"
    
  violation_response_protocol:
    immediate_halt: "❌ IMMEDIATE STOP of any non-MCP operation with detailed logging"
    forced_correction: "🔄 AUTOMATIC conversion to appropriate MCP operation"
    violation_escalation: "📝 LOG all violations with escalation to system administration"
    pattern_analysis: "🔍 ANALYZE violation patterns for systematic prevention"
    system_hardening: "🛡️ STRENGTHEN enforcement based on violation analysis"
    
  compliance_metrics:
    target_compliance: "100% MCP usage for all file and terminal operations"
    violation_tolerance: "0% - Zero tolerance for non-MCP operations"
    detection_accuracy: "≥99.9% accurate violation detection and prevention"
    correction_speed: "<1s automatic correction time with user notification"
    enforcement_reliability: "≥99.99% uptime for enforcement system"
    
  emergency_protocols:
    mcp_unavailability: "Graceful degradation with extensive logging and immediate escalation"
    system_failure: "Emergency protocols with automatic recovery and notification"
    critical_operations: "High-priority MCP operation queuing and expedited execution"
    disaster_recovery: "Complete MCP system restoration with backup activation"
```

---

**🔧 MCP ENFORCEMENT UNIFIED - SUPREME MCP AUTHORITY**
**100% MANDATORY USAGE + INTELLIGENT ROUTING + ZERO TOLERANCE**
**PRODUCTION SAFETY + OPTIMIZATION + QUALITY ≥9.7/10**