---
alwaysApply: true
---

# 🎯 CODING STANDARDS - <PERSON><PERSON><PERSON><PERSON> US VIBECODE V2.0

_Expert TypeScript, Next.js 14 App Router, React, Supabase, GraphQL, Tailwind CSS Standards_

**Core Principles**: "Aprimore, Não Prolifere" (≥85% reuse), Confidence ≥8/10, Quality ≥90%
**Stack**: TypeScript, Node.js, Next.js 14, React, Supabase, GraphQL, Genql, Tailwind CSS, Radix UI, Shadcn UI

---

## 🏗️ CORE DEVELOPMENT PHILOSOPHY

### **Professional Excellence Standards**

```json
{
  "core_principles": {
    "context_first": "Understand system completely before coding",
    "challenge_requests": "Identify edge cases, clarify requirements",
    "hold_standards": "Modular, testable, documented code",
    "design_not_patch": "Think architecture, not quick fixes",
    "execution_standards": "One file per response, no method renaming without approval"
  },
  "programming_paradigm": {
    "style": "functional, declarative programming",
    "avoid": "classes",
    "prefer": "iteration and modularization over duplication",
    "pattern": "RORO (Receive an Object, Return an Object)"
  }
}
```

### **Quality Enforcement**

- **Early Returns**: Handle errors at function beginning
- **Guard Clauses**: Use if-return pattern vs nested else
- **No Placeholders**: Complete functionality, no TODOs
- **Error Handling**: Comprehensive with proper logging
- **Type Safety**: TypeScript strict mode, interfaces over types

---

## 📝 TYPESCRIPT & JAVASCRIPT STANDARDS

### **Language Conventions**

```typescript
// ✅ CORRECT: Function keyword for pure functions, omit semicolons
function calculateTotal(items: Item[]): number {
  if (!items.length) return 0
  
  return items.reduce((sum, item) => sum + item.price, 0)
}

// ✅ CORRECT: Interfaces over types
interface UserProfile {
  id: string
  name: string
  email: string
  isActive: boolean
}

// ❌ WRONG: Using type instead of interface
type UserProfile = {
  id: string
  name: string
}

// ✅ CORRECT: Descriptive variable names with auxiliary verbs
const isLoading = false
const hasError = true
const shouldRender = true
```

### **File Structure Standards**

```typescript
// File order: Exported component, subcomponents, helpers, static content, types
// 1. Exported component
export function UserDashboard({ userId }: UserDashboardProps) {
  // Implementation
}

// 2. Subcomponents
function UserStats({ stats }: UserStatsProps) {
  // Implementation
}

// 3. Helper functions
function formatUserData(user: User): FormattedUser {
  // Implementation
}

// 4. Static content
const DASHBOARD_TABS = ['overview', 'settings', 'analytics'] as const

// 5. Types and interfaces
interface UserDashboardProps {
  userId: string
}

interface UserStatsProps {
  stats: UserStats
}
```

### **Conditional Statements**

```typescript
// ✅ CORRECT: Concise conditional statements, omit curly braces for single-line
if (condition) doSomething()

if (user.isActive) return user.profile

// ✅ CORRECT: Guard clauses for early returns
function processUser(user: User | null): ProcessedUser {
  if (!user) throw new Error('User is required')
  if (!user.isActive) throw new Error('User is not active')
  if (!user.profile) throw new Error('User profile is incomplete')
  
  // Happy path last
  return {
    id: user.id,
    displayName: user.profile.name,
    permissions: user.permissions
  }
}
```

---

## ⚠️ ERROR HANDLING & VALIDATION

### **Error Handling Patterns**

```typescript
// ✅ CORRECT: Early error handling, guard clauses
function validateUser(user: unknown): User {
  // Handle errors at the beginning
  if (!user) throw new Error('User data is required')
  if (typeof user !== 'object') throw new Error('Invalid user data format')
  
  const userData = user as Record<string, unknown>
  
  // Guard clauses for preconditions
  if (!userData.id) throw new Error('User ID is required')
  if (!userData.email) throw new Error('User email is required')
  if (typeof userData.email !== 'string') throw new Error('Invalid email format')
  
  // Happy path last
  return {
    id: userData.id as string,
    email: userData.email,
    name: userData.name as string || ''
  }
}

// ✅ CORRECT: Custom error types
class ValidationError extends Error {
  constructor(field: string, value: unknown) {
    super(`Validation failed for field '${field}' with value: ${value}`)
    this.name = 'ValidationError'
  }
}

// ✅ CORRECT: Error boundaries for unexpected errors
// error.tsx
export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h2>Something went wrong!</h2>
      <button onClick={() => reset()}>Try again</button>
    </div>
  )
}
```

### **Server Actions Error Handling**

```typescript
// ✅ CORRECT: Model expected errors as return values
async function createUser(formData: FormData): Promise<ActionResult<User>> {
  const validation = userSchema.safeParse({
    email: formData.get('email'),
    name: formData.get('name')
  })
  
  if (!validation.success) {
    return {
      success: false,
      error: 'Invalid user data',
      details: validation.error.issues
    }
  }
  
  try {
    const user = await db.user.create({
      data: validation.data
    })
    
    return { success: true, data: user }
  } catch (error) {
    return {
      success: false,
      error: 'Failed to create user'
    }
  }
}

// ✅ CORRECT: Use next-safe-action for type-safe server actions
import { createSafeAction } from 'next-safe-action'

const createUserAction = createSafeAction(userSchema, async (data) => {
  // Type-safe implementation
  const user = await db.user.create({ data })
  return user
})
```

---

## ⚛️ REACT & NEXT.JS 14 STANDARDS

### **Component Patterns**

```typescript
// ✅ CORRECT: Functional components with function keyword
function UserProfile({ user, onUpdate }: UserProfileProps) {
  // Minimize use client, favor Server Components
  return (
    <div className="space-y-4">
      <h1>{user.name}</h1>
      <UserActions onUpdate={onUpdate} />
    </div>
  )
}

// ✅ CORRECT: Named exports
export { UserProfile }

// ✅ CORRECT: Wrap client components in Suspense
import { Suspense } from 'react'

function UserDashboard() {
  return (
    <Suspense fallback={<DashboardSkeleton />}>
      <UserProfileClient />
    </Suspense>
  )
}

// ✅ CORRECT: Dynamic loading for non-critical components
import dynamic from 'next/dynamic'

const HeavyChart = dynamic(() => import('./heavy-chart'), {
  loading: () => <ChartSkeleton />
})
```

### **Form Handling with Validation**

```typescript
// ✅ CORRECT: Zod + react-hook-form + useActionState
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useActionState } from 'react'

const userSchema = z.object({
  email: z.string().email(),
  name: z.string().min(2)
})

function UserForm() {
  const [state, formAction] = useActionState(createUserAction, null)
  
  const form = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema)
  })
  
  return (
    <form action={formAction} className="space-y-4">
      <input
        {...form.register('email')}
        type="email"
        className="w-full px-3 py-2 border rounded"
      />
      {form.formState.errors.email && (
        <p className="text-red-500">{form.formState.errors.email.message}</p>
      )}
      
      <button type="submit" disabled={form.formState.isSubmitting}>
        Submit
      </button>
    </form>
  )
}
```

### **Data Fetching Patterns**

```typescript
// ✅ CORRECT: Server Components for data fetching
async function UserList() {
  const users = await getUsers() // Direct data fetching
  
  return (
    <div className="grid gap-4">
      {users.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  )
}

// ✅ CORRECT: Preload pattern to prevent waterfalls
function UserDashboard({ userId }: { userId: string }) {
  // Preload data
  const userPromise = getUser(userId)
  const postsPromise = getUserPosts(userId)
  
  return (
    <div>
      <Suspense fallback={<UserSkeleton />}>
        <UserProfile userPromise={userPromise} />
      </Suspense>
      <Suspense fallback={<PostsSkeleton />}>
        <UserPosts postsPromise={postsPromise} />
      </Suspense>
    </div>
  )
}
```

---

## 🤖 AI SDK INTEGRATION

### **Vercel AI SDK Patterns**

```typescript
// ✅ CORRECT: AI SDK Core with proper error handling
import { openai } from '@ai-sdk/openai'
import { generateText } from 'ai'

async function generateResponse(prompt: string): Promise<ActionResult<string>> {
  try {
    const { text } = await generateText({
      model: openai('gpt-4'),
      prompt,
      maxTokens: 1000,
    })
    
    return { success: true, data: text }
  } catch (error) {
    // Handle rate limiting
    if (error instanceof Error && error.message.includes('rate limit')) {
      return { 
        success: false, 
        error: 'Rate limit exceeded. Please try again later.' 
      }
    }
    
    return { 
      success: false, 
      error: 'Failed to generate response' 
    }
  }
}

// ✅ CORRECT: Streaming chat UI with error boundaries
'use client'

import { useChat } from 'ai/react'

function ChatInterface() {
  const { messages, input, handleInputChange, handleSubmit, error, isLoading } = useChat({
    api: '/api/chat',
    onError: (error) => {
      console.error('Chat error:', error)
      // Show user-friendly error message
    }
  })
  
  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto">
        {messages.map(message => (
          <ChatMessage key={message.id} message={message} />
        ))}
        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded">
            {error.message}
          </div>
        )}
      </div>
      
      <form onSubmit={handleSubmit} className="p-4">
        <input
          value={input}
          onChange={handleInputChange}
          disabled={isLoading}
          className="w-full px-3 py-2 border rounded"
          placeholder="Type your message..."
        />
      </form>
    </div>
  )
}

// ✅ CORRECT: Input sanitization
function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential XSS
    .slice(0, 1000) // Limit length
}
```

---

## 🗄️ SUPABASE & GRAPHQL STANDARDS

### **Supabase Client Patterns**

```typescript
// ✅ CORRECT: Type-safe Supabase client
import { createClient } from '@supabase/supabase-js'
import type { Database } from './database.types'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// ✅ CORRECT: Server-side client with RLS
import { createServerClient } from '@supabase/ssr'

function createSupabaseServer() {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookies().get(name)?.value
        },
      },
    }
  )
}

// ✅ CORRECT: Type-safe queries with error handling
async function getUser(id: string): Promise<User | null> {
  const { data, error } = await supabase
    .from('users')
    .select('id, email, profile(name, avatar_url)')
    .eq('id', id)
    .single()
  
  if (error) {
    if (error.code === 'PGRST116') return null // Not found
    throw new Error(`Failed to fetch user: ${error.message}`)
  }
  
  return data
}

// ✅ CORRECT: Real-time subscriptions
function useRealtimeUsers() {
  const [users, setUsers] = useState<User[]>([])
  
  useEffect(() => {
    const channel = supabase
      .channel('users-changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'users' },
        (payload) => {
          // Handle real-time updates
          setUsers(current => {
            switch (payload.eventType) {
              case 'INSERT':
                return [...current, payload.new as User]
              case 'UPDATE':
                return current.map(user => 
                  user.id === payload.new.id ? payload.new as User : user
                )
              case 'DELETE':
                return current.filter(user => user.id !== payload.old.id)
              default:
                return current
            }
          })
        }
      )
      .subscribe()
    
    return () => {
      supabase.removeChannel(channel)
    }
  }, [])
  
  return users
}
```

### **GraphQL with Genql**

```typescript
// ✅ CORRECT: Type-safe GraphQL queries
import { createClient } from './generated/client'

const client = createClient({
  url: 'https://api.example.com/graphql',
  headers: {
    Authorization: `Bearer ${token}`
  }
})

// ✅ CORRECT: Optimized queries - fetch only necessary data
async function getUserPosts(userId: string) {
  const { user } = await client.query({
    user: {
      __args: { id: userId },
      id: true,
      name: true,
      posts: {
        __args: { first: 10 },
        edges: {
          node: {
            id: true,
            title: true,
            createdAt: true,
            // Only fetch needed fields
          }
        }
      }
    }
  })
  
  return user
}
```

---

## 🎨 STYLING & UI STANDARDS

### **Tailwind CSS Conventions**

```typescript
// ✅ CORRECT: Utility-first approach, mobile-first responsive design
function ResponsiveCard({ title, children }: CardProps) {
  return (
    <div className="
      w-full
      p-4 md:p-6 lg:p-8
      bg-white dark:bg-gray-900
      border border-gray-200 dark:border-gray-800
      rounded-lg shadow-sm
      hover:shadow-md transition-shadow
    ">
      <h2 className="text-lg md:text-xl font-semibold mb-4">{title}</h2>
      {children}
    </div>
  )
}

// ✅ CORRECT: Class Variance Authority (CVA) for component variants
import { cva } from 'class-variance-authority'

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

// ✅ CORRECT: Shadcn UI component pattern
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline'
  size?: 'default' | 'sm' | 'lg'
}

function Button({ className, variant, size, ...props }: ButtonProps) {
  return (
    <button
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}
```

---

## 🚀 SUPERDESIGN INTEGRATION STANDARDS

### **AI-Powered UI Generation Guidelines**

```typescript
// ✅ CORRECT: SuperDesign generated component pattern
interface GeneratedComponentProps {
  title: string
  variant?: 'default' | 'primary' | 'secondary'
  className?: string
  children?: ReactNode
}

// SuperDesign generates base structure, then apply project conventions
export function GeneratedComponent({ 
  title, 
  variant = 'default', 
  className,
  children 
}: GeneratedComponentProps) {
  return (
    <div className={cn(
      // Base styles generated by SuperDesign
      'rounded-lg border bg-card p-6 shadow-sm',
      // Variant styles following CVA pattern
      variantStyles[variant],
      // Custom overrides
      className
    )}>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      {children}
    </div>
  )
}

// ✅ CORRECT: Post-generation refinement process
// 1. SuperDesign generates initial structure
// 2. Apply TypeScript interfaces
// 3. Add project-specific styling conventions
// 4. Ensure accessibility compliance
// 5. Test responsive behavior
```

### **SuperDesign Workflow Integration**

```markdown
## Component Generation Process
1. **Analysis**: Determine if SuperDesign should generate the component
2. **Generation**: Use SuperDesign sidebar with specific requirements
3. **Refinement**: Apply project coding standards
4. **Validation**: Test component against quality checklist
5. **Documentation**: Document props and usage examples

## Quality Checklist for Generated Components
- [ ] TypeScript interfaces properly defined
- [ ] Tailwind CSS classes follow project conventions
- [ ] Component is responsive across breakpoints
- [ ] Accessibility attributes included (ARIA labels, focus management)
- [ ] Component exports follow naming conventions
- [ ] Props are documented with JSDoc comments
- [ ] Component integrates with existing design system
```

### **SuperDesign + Copilot Synergy**

```typescript
// ✅ CORRECT: Combining SuperDesign generation with Copilot refinement
// Step 1: SuperDesign generates base component
// Step 2: Copilot helps refine and optimize

// SuperDesign generates structure, Copilot adds business logic
function DataWidget({ data, onSelect }: DataWidgetProps) {
  // SuperDesign: Base UI structure
  // Copilot: Data processing and event handling
  const processedData = useMemo(() => 
    data.map(item => ({
      ...item,
      displayValue: formatValue(item.value)
    })), [data]
  )

  return (
    <div className="widget-container">
      {/* SuperDesign: UI layout */}
      {/* Copilot: Interactive logic */}
    </div>
  )
}
```

### **Template Management**

```typescript
// ✅ CORRECT: Custom SuperDesign templates
// Location: .superdesign/templates/

// Template structure for consistent generation
interface TemplateDefinition {
  name: string
  category: 'layout' | 'component' | 'widget' | 'form'
  props: PropDefinition[]
  styling: 'tailwind' | 'css-modules' | 'styled-components'
  responsive: boolean
  accessibility: boolean
}

// Available custom templates:
// - neon-card: Modern cards with glow effects
// - dashboard-widget: Analytics widgets
// - data-table: Complex data tables
// - form-field: Consistent form components
// - modal-dialog: Accessible modals
```

---

## 📁 NAMING CONVENTIONS & FILE STRUCTURE

### **Naming Standards**

```typescript
// ✅ CORRECT: Descriptive variable names with auxiliary verbs
const isUserActive = true
const hasPermission = false
const shouldShowModal = true
const doesSupportFeature = false

// ✅ CORRECT: File naming conventions
// components/auth-wizard.tsx
// hooks/use-auth.hook.ts
// types/user.type.ts
// config/database.config.ts
// tests/auth.test.ts
// contexts/theme.context.tsx

// ✅ CORRECT: Directory structure (lowercase with dashes)
/*
src/
├── components/
│   ├── auth-wizard/
│   │   ├── auth-wizard.tsx
│   │   ├── auth-steps.tsx
│   │   └── index.ts
│   └── ui/
├── hooks/
├── lib/
└── types/
*/
```

### **Component Structure**

```typescript
// ✅ CORRECT: Micro folder structure for complex components
/*
components/
├── user-dashboard/
│   ├── user-dashboard.tsx          # Main component
│   ├── user-stats.tsx             # Subcomponent
│   ├── user-actions.tsx           # Subcomponent
│   ├── user-dashboard.types.ts    # Types
│   ├── user-dashboard.hooks.ts    # Custom hooks
│   └── index.ts                   # Export
*/

// ✅ CORRECT: Break down components with minimal props
function UserDashboard({ userId }: { userId: string }) {
  return (
    <div className="space-y-6">
      <UserHeader userId={userId} />
      <UserStats userId={userId} />
      <UserActions userId={userId} />
    </div>
  )
}

// ✅ CORRECT: Composition over complex props
function UserCard({ user, actions, metadata }: UserCardProps) {
  return (
    <div className="card">
      <UserAvatar user={user} />
      <UserInfo user={user} metadata={metadata} />
      <UserActions>{actions}</UserActions>
    </div>
  )
}
```

---

## 🧪 TESTING STANDARDS

```typescript
// ✅ CORRECT: Unit tests for utility functions
import { describe, it, expect } from 'vitest'
import { formatCurrency } from './currency.utils'

describe('formatCurrency', () => {
  it('should format USD currency correctly', () => {
    expect(formatCurrency(1234.56, 'USD')).toBe('$1,234.56')
  })
  
  it('should handle zero values', () => {
    expect(formatCurrency(0, 'USD')).toBe('$0.00')
  })
  
  it('should throw for invalid currency', () => {
    expect(() => formatCurrency(100, 'INVALID')).toThrow()
  })
})

// ✅ CORRECT: Integration tests for components
import { render, screen, fireEvent } from '@testing-library/react'
import { UserForm } from './user-form'

describe('UserForm', () => {
  it('should submit form with valid data', async () => {
    const mockOnSubmit = vi.fn()
    render(<UserForm onSubmit={mockOnSubmit} />)
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    
    fireEvent.click(screen.getByRole('button', { name: /submit/i }))
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        email: '<EMAIL>'
      })
    })
  })
})
```

---

## ♿ ACCESSIBILITY STANDARDS

```typescript
// ✅ CORRECT: Keyboard navigation and ARIA labels
function Modal({ isOpen, onClose, children }: ModalProps) {
  const dialogRef = useRef<HTMLDialogElement>(null)
  
  useEffect(() => {
    const dialog = dialogRef.current
    if (!dialog) return
    
    if (isOpen) {
      dialog.showModal()
      // Focus trap
      const focusableElements = dialog.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
      const firstElement = focusableElements[0] as HTMLElement
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
      
      firstElement?.focus()
    } else {
      dialog.close()
    }
  }, [isOpen])
  
  return (
    <dialog
      ref={dialogRef}
      className="backdrop:bg-black/50 p-6 rounded-lg"
      onClose={onClose}
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <div className="flex justify-between items-center mb-4">
        <h2 id="modal-title" className="text-xl font-semibold">
          Modal Title
        </h2>
        <button
          onClick={onClose}
          aria-label="Close modal"
          className="p-2 hover:bg-gray-100 rounded"
        >
          ×
        </button>
      </div>
      <div id="modal-description">
        {children}
      </div>
    </dialog>
  )
}

// ✅ CORRECT: Color contrast and semantic HTML
function StatusBadge({ status }: { status: 'active' | 'inactive' | 'pending' }) {
  const variants = {
    active: 'bg-green-100 text-green-800 border-green-200',
    inactive: 'bg-red-100 text-red-800 border-red-200',
    pending: 'bg-yellow-100 text-yellow-800 border-yellow-200'
  }
  
  return (
    <span
      className={`px-2 py-1 text-xs font-medium rounded-full border ${variants[status]}`}
      role="status"
      aria-label={`Status: ${status}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  )
}
```

---

## 📚 DOCUMENTATION STANDARDS

```typescript
/**
 * Calculates the total price including tax for a list of items
 * 
 * @param items - Array of items with price and tax rate
 * @param discountPercent - Optional discount percentage (0-100)
 * @returns Object containing subtotal, tax, discount, and total
 * 
 * @example
 * ```typescript
 * const result = calculateTotal([
 *   { price: 100, taxRate: 0.1 }
 * ], 10)
 * console.log(result.total) // 99
 * ```
 */
function calculateTotal(
  items: PriceItem[],
  discountPercent?: number
): PriceCalculation {
  // Guard clauses
  if (!items.length) {
    return { subtotal: 0, tax: 0, discount: 0, total: 0 }
  }
  
  const subtotal = items.reduce((sum, item) => sum + item.price, 0)
  const tax = items.reduce((sum, item) => sum + (item.price * item.taxRate), 0)
  const discount = discountPercent ? subtotal * (discountPercent / 100) : 0
  const total = subtotal + tax - discount
  
  return { subtotal, tax, discount, total }
}

/**
 * Custom hook for managing user authentication state
 * 
 * @returns Authentication state and helper functions
 */
function useAuth(): AuthState {
  // Implementation with clear JSDoc
}
```

---

## 🛡️ LOCAL FILES SOVEREIGNTY POLICY

### **Git Configuration Standards**

```bash
# Force push with lease (safer)
git config --global alias.pushf "push --force-with-lease"

# Push to all remotes with force
git config --global alias.pushall "!git remote | xargs -L1 git push --force-with-lease --all"

# Daily development workflow
git add .
git commit -m "feat: implement user authentication"
git pushf
```

---

## 📊 PERFORMANCE STANDARDS

### **Web Vitals Optimization**

```typescript
// ✅ CORRECT: Image optimization
import Image from 'next/image'

function OptimizedImage({ src, alt }: ImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      width={800}
      height={600}
      format="webp"
      loading="lazy"
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
      className="rounded-lg"
    />
  )
}

// ✅ CORRECT: Bundle optimization
import dynamic from 'next/dynamic'

const HeavyComponent = dynamic(() => import('./heavy-component'), {
  loading: () => <ComponentSkeleton />,
  ssr: false
})

// ✅ CORRECT: React performance patterns
import { memo, useMemo, useCallback } from 'react'

const ExpensiveList = memo(function ExpensiveList({ items, onItemClick }: ListProps) {
  const sortedItems = useMemo(
    () => items.sort((a, b) => a.name.localeCompare(b.name)),
    [items]
  )
  
  const handleItemClick = useCallback(
    (id: string) => onItemClick(id),
    [onItemClick]
  )
  
  return (
    <ul>
      {sortedItems.map(item => (
        <ListItem key={item.id} item={item} onClick={handleItemClick} />
      ))}
    </ul>
  )
})
```

---

## ✅ QUALITY CHECKLIST

### **Pre-commit Checklist**
- [ ] TypeScript strict mode passes
- [ ] All interfaces properly defined
- [ ] Error handling implemented with guard clauses
- [ ] Components use proper naming conventions
- [ ] Accessibility attributes included
- [ ] Performance optimizations applied
- [ ] Tests cover critical paths
- [ ] JSDoc comments for complex functions

### **Performance Targets**
- **LCP (Largest Contentful Paint)**: <2.5s
- **FID (First Input Delay)**: <100ms
- **CLS (Cumulative Layout Shift)**: <0.1
- **TTI (Time to Interactive)**: <3.8s
- **Bundle size**: <200KB gzipped

### **Key Conventions Compliance**
1. ✅ Next.js App Router for state changes and routing
2. ✅ Minimize 'use client' usage - prefer Server Components
3. ✅ TypeScript interfaces over types
4. ✅ Function keyword for components
5. ✅ RORO pattern implementation
6. ✅ Early returns and guard clauses
7. ✅ Proper error boundaries
8. ✅ Mobile-first responsive design

---

**Remember**: "Context First, Quality Always, Performance Matters"
**Authority**: E:\VIBECODE\.cursor\rules\coding-standards.mdc
**Version**: 2.0.0 - Enhanced with TypeScript/Next.js 14 specifics