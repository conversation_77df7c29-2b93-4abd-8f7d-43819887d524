#!/usr/bin/env python3
"""
Análise de Uso de Arquivos - VIBECODE V4.0
Identifica arquivos obsoletos, redundantes ou não utilizados na arquitetura modular
"""

import os
import re
import glob
from pathlib import Path

class FileUsageAnalyzer:
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.master_file = self.base_path / ".github" / "copilot-instructions.md"
        self.instructions_dir = self.base_path / ".github" / "instructions"
        self.rules_dir = self.base_path / ".github" / "rules"
        self.context_engine_dir = self.base_path / ".github" / "context-engine"
        
        # Novos módulos modularizados
        self.modular_files = [
            "development-patterns.md",
            "agent-behaviors.md", 
            "research-protocols.md",
            "system-architecture.md",
            "automation-tools.md"
        ]
        
        # Arquivos que devem ser mantidos (core do sistema)
        self.core_files = [
            "copilot-instructions.md",
            "memory-bank.instructions.md",
            "performance-optimization.instructions.md",
            "MODULAR-ARCHITECTURE-DOCUMENTATION.md"
        ]

    def read_file_content(self, file_path):
        """Lê o conteúdo de um arquivo"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"❌ Erro ao ler {file_path}: {e}")
            return ""

    def find_file_references(self, content, file_name):
        """Procura referências a um arquivo no conteúdo"""
        patterns = [
            rf'\b{re.escape(file_name)}\b',
            rf'\.github/instructions/{re.escape(file_name)}',
            rf'instructions/{re.escape(file_name)}',
            rf'{re.escape(file_name.replace(".md", ""))}',
        ]
        
        references = []
        for pattern in patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            references.extend([match.group() for match in matches])
        
        return references

    def analyze_master_file(self):
        """Analisa o arquivo master para ver quais arquivos ele referencia"""
        print("🔍 Analisando arquivo master copilot-instructions.md...")
        
        if not self.master_file.exists():
            print("❌ Arquivo master não encontrado!")
            return {}
            
        master_content = self.read_file_content(self.master_file)
        
        referenced_files = {}
        
        # Busca por todos os arquivos .md nas pastas relevantes
        all_md_files = []
        for directory in [self.instructions_dir, self.rules_dir, self.context_engine_dir]:
            if directory.exists():
                all_md_files.extend(directory.glob("*.md"))
        
        for md_file in all_md_files:
            file_name = md_file.name
            references = self.find_file_references(master_content, file_name)
            
            if references:
                referenced_files[file_name] = {
                    "path": str(md_file),
                    "references_count": len(references),
                    "references": list(set(references))[:3]  # Primeiras 3 referências únicas
                }
        
        return referenced_files

    def analyze_cross_references(self):
        """Analisa referências cruzadas entre os módulos"""
        print("🔗 Analisando referências cruzadas entre módulos...")
        
        cross_refs = {}
        
        for module_file in self.modular_files:
            module_path = self.instructions_dir / module_file
            if not module_path.exists():
                continue
                
            module_content = self.read_file_content(module_path)
            cross_refs[module_file] = {}
            
            # Procura referências a outros módulos
            for other_module in self.modular_files:
                if other_module != module_file:
                    other_name = other_module.replace(".md", "")
                    references = self.find_file_references(module_content, other_module)
                    if references:
                        cross_refs[module_file][other_module] = len(references)
        
        return cross_refs

    def identify_potentially_obsolete(self):
        """Identifica arquivos potencialmente obsoletos"""
        print("🗑️ Identificando arquivos potencialmente obsoletos...")
        
        referenced_files = self.analyze_master_file()
        
        potentially_obsolete = []
        potentially_redundant = []
        
        # Lista todos os arquivos .md em instructions
        for md_file in self.instructions_dir.glob("*.md"):
            file_name = md_file.name
            
            # Pula arquivos core e modularizados
            if file_name in self.core_files or file_name in self.modular_files:
                continue
            
            # Verifica se o arquivo é referenciado no master
            if file_name not in referenced_files:
                potentially_obsolete.append({
                    "file": file_name,
                    "path": str(md_file),
                    "reason": "Não referenciado no arquivo master"
                })
            
            # Verifica redundância com módulos modularizados
            if self.check_redundancy(md_file):
                potentially_redundant.append({
                    "file": file_name,
                    "path": str(md_file),
                    "reason": "Funcionalidade já coberta pelos novos módulos"
                })
        
        return potentially_obsolete, potentially_redundant

    def check_redundancy(self, file_path):
        """Verifica se um arquivo é redundante com os novos módulos"""
        file_name = file_path.name.lower()
        
        # Mapas de redundância conhecida
        redundancy_map = {
            "architecture.md": "system-architecture.md",  # Redundante com system-architecture.md
            "quality-standards-framework.md": "development-patterns.md",  # Qualidade está em development-patterns
            "testing.md": "development-patterns.md",  # Testing patterns estão em development-patterns
        }
        
        return file_name in redundancy_map

    def analyze_content_overlap(self):
        """Analisa sobreposição de conteúdo entre arquivos"""
        print("📊 Analisando sobreposição de conteúdo...")
        
        overlaps = []
        
        # Compara arquivos antigos com novos módulos
        old_files = ["architecture.md", "quality-standards-framework.md", "testing.md"]
        
        for old_file in old_files:
            old_path = self.instructions_dir / old_file
            if not old_path.exists():
                continue
                
            old_content = self.read_file_content(old_path).lower()
            
            for new_module in self.modular_files:
                new_path = self.instructions_dir / new_module
                if not new_path.exists():
                    continue
                    
                new_content = self.read_file_content(new_path).lower()
                
                # Análise simples de palavras-chave comuns
                old_words = set(re.findall(r'\b\w{4,}\b', old_content))
                new_words = set(re.findall(r'\b\w{4,}\b', new_content))
                
                common_words = old_words & new_words
                overlap_ratio = len(common_words) / len(old_words) if old_words else 0
                
                if overlap_ratio > 0.3:  # Mais de 30% de sobreposição
                    overlaps.append({
                        "old_file": old_file,
                        "new_module": new_module,
                        "overlap_ratio": f"{overlap_ratio:.2%}",
                        "common_concepts": len(common_words)
                    })
        
        return overlaps

    def generate_report(self):
        """Gera relatório completo da análise"""
        print("\n" + "="*60)
        print("📋 RELATÓRIO DE ANÁLISE DE USO DE ARQUIVOS")
        print("="*60)
        
        # 1. Arquivos referenciados no master
        referenced = self.analyze_master_file()
        print(f"\n✅ ARQUIVOS REFERENCIADOS NO MASTER ({len(referenced)}):")
        for file_name, data in referenced.items():
            print(f"  📁 {file_name} - {data['references_count']} referências")
        
        # 2. Referências cruzadas
        cross_refs = self.analyze_cross_references()
        print(f"\n🔗 REFERÊNCIAS CRUZADAS ENTRE MÓDULOS:")
        for module, refs in cross_refs.items():
            if refs:
                print(f"  📁 {module}:")
                for ref_module, count in refs.items():
                    print(f"    → {ref_module}: {count} referências")
        
        # 3. Arquivos potencialmente obsoletos
        obsolete, redundant = self.identify_potentially_obsolete()
        
        if obsolete:
            print(f"\n⚠️ ARQUIVOS POTENCIALMENTE OBSOLETOS ({len(obsolete)}):")
            for item in obsolete:
                print(f"  🗑️ {item['file']} - {item['reason']}")
        
        if redundant:
            print(f"\n🔄 ARQUIVOS POTENCIALMENTE REDUNDANTES ({len(redundant)}):")
            for item in redundant:
                print(f"  ⚡ {item['file']} - {item['reason']}")
        
        # 4. Análise de sobreposição
        overlaps = self.analyze_content_overlap()
        if overlaps:
            print(f"\n📊 SOBREPOSIÇÃO DE CONTEÚDO:")
            for overlap in overlaps:
                print(f"  🔄 {overlap['old_file']} ↔ {overlap['new_module']}: {overlap['overlap_ratio']} ({overlap['common_concepts']} conceitos comuns)")
        
        # 5. Recomendações
        print(f"\n💡 RECOMENDAÇÕES:")
        
        if obsolete:
            print("  🗑️ ARQUIVOS PARA REMOÇÃO SEGURA:")
            for item in obsolete:
                print(f"    - {item['file']} (não referenciado)")
        
        if redundant:
            print("  🔄 ARQUIVOS REDUNDANTES (verificar antes de remover):")
            for item in redundant:
                print(f"    - {item['file']} (funcionalidade já coberta)")
        
        # 6. Validação da arquitetura modular
        all_modules_exist = all((self.instructions_dir / module).exists() for module in self.modular_files)
        master_exists = self.master_file.exists()
        
        print(f"\n🎯 STATUS DA ARQUITETURA MODULAR:")
        print(f"  ✅ Arquivo master: {'✓' if master_exists else '❌'}")
        print(f"  ✅ Todos os módulos: {'✓' if all_modules_exist else '❌'}")
        print(f"  ✅ Referências no master: {'✓' if len(referenced) >= len(self.modular_files) else '❌'}")
        
        if all_modules_exist and master_exists:
            print("\n🎉 ARQUITETURA MODULAR ESTÁ OPERACIONAL!")
        else:
            print("\n⚠️ PROBLEMAS DETECTADOS NA ARQUITETURA MODULAR!")
        
        print("\n" + "="*60)
        return {
            "referenced": referenced,
            "obsolete": obsolete,
            "redundant": redundant,
            "overlaps": overlaps,
            "cross_refs": cross_refs
        }

def main():
    print("🚀 Iniciando Análise de Uso de Arquivos - VIBECODE V4.0")
    print("="*60)
    
    # Detecta o diretório base automaticamente
    base_path = Path(__file__).parent.parent.parent
    
    analyzer = FileUsageAnalyzer(base_path)
    results = analyzer.generate_report()
    
    print(f"\n✅ Análise concluída!")
    print(f"📊 Resultados disponíveis para ação")

if __name__ == "__main__":
    main()
