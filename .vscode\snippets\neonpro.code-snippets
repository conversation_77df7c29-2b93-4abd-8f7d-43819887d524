{"NeonPro Server Component": {"prefix": "neon-server", "body": ["import { createClient } from '@/app/utils/supabase/server'", "import { redirect } from 'next/navigation'", "import DashboardLayout from '@/components/layouts/dashboard-layout'", "", "export default async function ${1:ComponentName}() {", "  const supabase = await createClient()", "  const { data: { session } } = await supabase.auth.getSession()", "", "  if (!session) {", "    redirect('/login')", "  }", "", "  const { data: { user } } = await supabase.auth.getUser()", "", "  const breadcrumbs = [", "    { title: 'Dashboard', href: '/dashboard' },", "    { title: '${2:Page Title}' }", "  ]", "", "  return (", "    <DashboardLayout user={user} breadcrumbs={breadcrumbs}>", "      <div className=\"space-y-6\">", "        <div className=\"flex items-center justify-between\">", "          <h1 className=\"text-3xl font-bold tracking-tight\">${2:Page Title}</h1>", "        </div>", "        ${4:// Page content}", "      </div>", "    </DashboardLayout>", "  )", "}"], "description": "NeonPro Server Component with authentication and dashboard layout"}, "NeonPro Client Component": {"prefix": "neon-client", "body": ["\"use client\"", "", "import { useState } from 'react'", "import { createClient } from '@/app/utils/supabase/client'", "import { Button } from '@/components/ui/button'", "import { toast } from 'sonner'", "", "export default function ${1:ComponentName}() {", "  const [isLoading, setIsLoading] = useState(false)", "  const supabase = createClient()", "", "  const handleAction = async () => {", "    try {", "      setIsLoading(true)", "      ${2:// Client-side logic}", "      toast.success('${3:Success message}')", "    } catch (error) {", "      console.error('Error:', error)", "      toast.error('${4:Error message}')", "    } finally {", "      setIsLoading(false)", "    }", "  }", "", "  return (", "    <div className=\"${5:space-y-4}\">", "      ${6:// Component content}", "    </div>", "  )", "}"], "description": "NeonPro Client Component with error handling"}, "NeonPro Form Component": {"prefix": "neon-form", "body": ["\"use client\"", "", "import { useForm } from 'react-hook-form'", "import { zodResolver } from '@hookform/resolvers/zod'", "import { z } from 'zod'", "import { Button } from '@/components/ui/button'", "import {", "  Form,", "  FormControl,", "  <PERSON><PERSON><PERSON>,", "  FormItem,", "  FormLabel,", "  FormMessage,", "} from '@/components/ui/form'", "import { Input } from '@/components/ui/input'", "import { toast } from 'sonner'", "", "const formSchema = z.object({", "  ${1:fieldName}: z.string().min(1, '${2:Field is required}'),", "})", "", "type FormData = z.infer<typeof formSchema>", "", "export default function ${3:FormName}() {", "  const form = useForm<FormData>({", "    resolver: zod<PERSON>esolver(formSchema),", "    defaultValues: {", "      ${1:fieldName}: '',", "    },", "  })", "", "  const onSubmit = async (data: FormData) => {", "    try {", "      ${4:// Handle form submission}", "      toast.success('${5:Success message}')", "      form.reset()", "    } catch (error) {", "      console.error('Form error:', error)", "      toast.error('${6:Error message}')", "    }", "  }", "", "  return (", "    <Form {...form}>", "      <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">", "        <FormField", "          control={form.control}", "          name=\"${1:fieldName}\"", "          render={({ field }) => (", "            <FormItem>", "              <FormLabel>${7:Field Label}</FormLabel>", "              <FormControl>", "                <Input placeholder=\"${8:Enter value}\" {...field} />", "              </FormControl>", "              <FormMessage />", "            </FormItem>", "          )}", "        />", "        <Button type=\"submit\" disabled={form.formState.isSubmitting}>", "          {form.formState.isSubmitting ? 'Submitting...' : '${9:Submit}'}", "        </Button>", "      </form>", "    </Form>", "  )", "}"], "description": "NeonPro Form Component with validation"}, "NeonPro API Route": {"prefix": "neon-api", "body": ["import { createClient } from '@/app/utils/supabase/server'", "import { NextRequest, NextResponse } from 'next/server'", "import { z } from 'zod'", "", "const requestSchema = z.object({", "  ${1:fieldName}: z.string(),", "})", "", "export async function ${2:POST}(request: NextRequest) {", "  try {", "    const supabase = await createClient()", "    const { data: { session } } = await supabase.auth.getSession()", "", "    if (!session) {", "      return NextResponse.json(", "        { error: 'Unauthorized' },", "        { status: 401 }", "      )", "    }", "", "    const body = await request.json()", "    const validatedData = requestSchema.parse(body)", "", "    ${3:// API logic here}", "", "    return NextResponse.json({ success: true, data: ${4:result} })", "  } catch (error) {", "    console.error('API Error:', error)", "    ", "    if (error instanceof z<PERSON>) {", "      return NextResponse.json(", "        { error: 'Invalid request data', details: error.errors },", "        { status: 400 }", "      )", "    }", "", "    return NextResponse.json(", "      { error: 'Internal server error' },", "      { status: 500 }", "    )", "  }", "}"], "description": "NeonPro API Route with authentication and validation"}, "Supabase Query with Error Handling": {"prefix": "neon-query", "body": ["const { data: ${1:dataName}, error } = await supabase", "  .from('${2:table_name}')", "  .select('${3:*}')", "  ${4:.eq('column', value)}", "", "if (error) {", "  console.error('Database error:', error)", "  throw new Error('${5:Failed to fetch data}')", "}", "", "return ${1:dataName}"], "description": "Supabase query with proper error handling"}, "Supabase RLS Insert": {"prefix": "neon-insert", "body": ["const { data: ${1:insertedData}, error } = await supabase", "  .from('${2:table_name}')", "  .insert({", "    ${3:column}: ${4:value},", "    user_id: session.user.id,", "    created_at: new Date().toISOString(),", "  })", "  .select()", "  .single()", "", "if (error) {", "  console.error('Insert error:', error)", "  throw new Error('${5:Failed to create record}')", "}", "", "return ${1:insertedData}"], "description": "Supabase insert with RLS compliance"}, "shadcn/ui Card Layout": {"prefix": "neon-card", "body": ["import {", "  Card,", "  <PERSON><PERSON><PERSON><PERSON>,", "  CardDescription,", "  <PERSON><PERSON><PERSON><PERSON>,", "  <PERSON><PERSON><PERSON><PERSON>,", "} from '@/components/ui/card'", "", "<Card className=\"${1:w-full}\">", "  <CardHeader>", "    <CardTitle>${2:Card Title}</CardTitle>", "    <CardDescription>", "      ${3:Card description}", "    </CardDescription>", "  </CardHeader>", "  <CardContent className=\"${4:space-y-4}\">", "    ${5:// Card content}", "  </CardContent>", "</Card>"], "description": "shadcn/ui Card component structure"}, "Loading Button": {"prefix": "neon-button-loading", "body": ["import { Loader2 } from 'lucide-react'", "import { Button } from '@/components/ui/button'", "", "<Button disabled={${1:isLoading}} className=\"${2:w-full}\">", "  {${1:isLoading} && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}", "  ${3:Button Text}", "</Button>"], "description": "Button with loading state"}, "Toast Notification": {"prefix": "neon-toast", "body": ["import { toast } from 'sonner'", "", "// Success", "toast.success('${1:Success message}')", "", "// Error", "toast.error('${2:Error message}')", "", "// Loading", "const toastId = toast.loading('${3:Loading message}')", "", "// Update loading toast", "toast.success('${4:Completed!}', { id: toastId })"], "description": "Toast notification patterns"}, "BMad Story Implementation": {"prefix": "neon-bmad-story", "body": ["// Story: ${1:Story Name}", "// Implementation following BMad Method patterns", "", "\"use client\"", "", "import { useState, useEffect } from 'react'", "import { createClient } from '@/app/utils/supabase/client'", "import { toast } from 'sonner'", "", "export default function ${2:ComponentName}() {", "  const [isLoading, setIsLoading] = useState(false)", "  const [data, setData] = useState(null)", "  const supabase = createClient()", "", "  useEffect(() => {", "    // Load initial data", "    const loadData = async () => {", "      try {", "        setIsLoading(true)", "        ${3:// Fetch data logic}", "      } catch (error) {", "        console.error('Error:', error)", "        toast.error('Failed to load data')", "      } finally {", "        setIsLoading(false)", "      }", "    }", "", "    loadData()", "  }, [])", "", "  return (", "    <div className=\"${4:space-y-6}\">", "      <div className=\"flex items-center justify-between\">", "        <h2 className=\"text-2xl font-bold\">${1:Story Name}</h2>", "      </div>", "      ", "      {isLoading ? (", "        <div className=\"flex items-center justify-center py-8\">", "          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\" />", "        </div>", "      ) : (", "        <div className=\"${5:grid gap-4}\">", "          ${6:// Story implementation content}", "        </div>", "      )}", "    </div>", "  )", "}"], "description": "BMad Method story implementation template"}}