# 🎯 VIBECODE V6.0 - Matriz Central de Triggers para Orquestração Modular
# Coordenação Inteligente: copilot-instructions.md ↔ voidbeast-modular.chatmode.md

orchestration_config:
  version: "6.0"
  hub_coordinator: ".github/copilot-instructions.md"
  specialized_orchestrator: ".github/chatmodes/voidbeast-modular.chatmode.md"
  performance_target: "85%+ context optimization"
  quality_threshold: "≥9.5/10"

# 🧠 SISTEMA DE DETECÇÃO DE COMPLEXIDADE (APEX V5.0 Enhanced)
complexity_detection:
  scoring_algorithm:
    base_score: 1.0
    cognitive_multiplier: 1.0-4.0  # L1-L4 keywords
    technical_multiplier: 1.0-3.0  # Surface → Expert depth
    scope_multiplier: 1.0-2.5      # Single → Enterprise scope
    risk_multiplier: 1.0-2.0       # Low → Critical risk
    
  complexity_levels:
    L1_simple:
      range: "1.0-3.0"
      context_loading: "minimal"
      orchestrator: "copilot-instructions.md (direct)"
      instruction_modules: []
      mcp_chain: ["desktop-commander"]
      
    L2_moderate:
      range: "3.1-5.5"
      context_loading: "selective"
      orchestrator: "specialized chatmode delegation"
      instruction_modules: ["targeted modules"]
      mcp_chain: ["context7", "desktop-commander"]
      
    L3_complex:
      range: "5.6-7.5"
      context_loading: "comprehensive"
      orchestrator: "voidbeast-modular.chatmode.md"
      instruction_modules: ["multiple modules"]
      mcp_chain: ["context7", "tavily", "sequential-thinking", "desktop-commander"]
      
    L4_enterprise:
      range: "7.6-10.0"
      context_loading: "full orchestration"
      orchestrator: "full APEX coordination"
      instruction_modules: ["all relevant modules"]
      mcp_chain: ["context7", "tavily", "exa", "sequential-thinking", "desktop-commander"]

# 🎛️ MAPEAMENTO DE TRIGGERS PARA MÓDULOS DE INSTRUÇÃO
trigger_routing_matrix:
  
  # MCP & Research Operations
  mcp_research_operations:
    keywords: ["mcp", "research", "pesquisar", "investigar", "analisar", "estudar", "comparar"]
    complexity_threshold: 3.1
    instruction_modules: 
      - ".github/instructions/mcp.instructions.md"
      - ".github/instructions/research-protocols.instructions.md"
    rules_activation:
      - ".github/rules/core/mcp-enforcement.md"
    orchestrator_delegation: "voidbeast-modular.chatmode.md"
    
  # Memory Bank & Context Operations  
  memory_context_operations:
    keywords: ["lembre", "contexto", "continue", "próxima", "implementar", "neonpro"]
    complexity_threshold: 2.0
    instruction_modules:
      - ".github/instructions/memory-bank.instructions.md"
    rules_activation:
      - ".github/rules/core/memory-optimization.md"
    orchestrator_delegation: "copilot-instructions.md"
    
  # Development & Implementation
  development_operations:
    keywords: ["code", "implement", "build", "develop", "create", "fix", "desenvolver", "criar", "construir"]
    complexity_threshold: 3.5
    instruction_modules:
      - ".github/instructions/development-patterns.instructions.md"
      - ".github/instructions/performance-optimization.instructions.md"
    rules_activation:
      - ".github/rules/workflows/development-workflow.md"
    orchestrator_delegation: "specialized chatmode (complexity-based)"
    
  # Architecture & Design
  architecture_operations:
    keywords: ["architecture", "design", "structure", "system", "blueprint", "arquitetura", "estrutura"]
    complexity_threshold: 5.6
    instruction_modules:
      - ".github/instructions/system-architecture.instructions.md"
      - ".github/instructions/agent-behaviors.instructions.md"
    rules_activation:
      - ".github/rules/core/quality-enforcement.md"
      - ".github/rules/workflows/architecture-review.md"
    orchestrator_delegation: "voidbeast-modular.chatmode.md → APEX delegation"
    
  # BMad Method Operations
  bmad_operations:
    keywords: ["*help", "*kb", "*task", "*create-doc", "*execute-checklist", "bmad", "template", "checklist"]
    complexity_threshold: 2.0
    instruction_modules:
      - ".github/instructions/bmad-integration.instructions.md"
    rules_activation:
      - ".github/rules/workflows/bmad-workflow.md"
    orchestrator_delegation: ".github/chatmodes/bmad-master.chatmode.md"
    
  # Debugging & Quality Assurance
  debug_qa_operations:
    keywords: ["debug", "erro", "corrigir", "troubleshoot", "qa", "test", "validate"]
    complexity_threshold: 4.0
    instruction_modules:
      - ".github/instructions/debugging-protocols.instructions.md"
      - ".github/instructions/quality-assurance.instructions.md"
    rules_activation:
      - ".github/rules/core/quality-enforcement.md"
    orchestrator_delegation: "complexity-based (≥7.0 → APEX QA Debugger)"

# ⚡ CONFIGURAÇÕES DE PERFORMANCE
performance_optimization:
  context_loading:
    lazy_loading: true
    cache_duration: "5 minutes"
    max_concurrent_modules: 3
    progressive_enhancement: true
    
  memory_management:
    smart_activation: true
    context_compression: "85%+ reduction"
    module_lifecycle: "request-scoped"
    
  monitoring:
    load_time_target: "<200ms"
    cache_hit_rate: ">90%"
    context_relevance: ">95%"

# 🔄 PROTOCOLOS DE DELEGAÇÃO
delegation_protocols:
  copilot_to_voidbeast:
    trigger_condition: "complexity ≥5.6 OR specialized GitHub Copilot needs"
    context_preservation: "full context handoff"
    quality_continuity: "≥9.5/10 maintained"
    
  voidbeast_to_specialized:
    trigger_condition: "domain-specific expertise required"
    available_chatmodes:
      - "neonpro-development.chatmode.md"
      - "bmad-master.chatmode.md"
      - "apex-architect.chatmode.md"
      - "apex-qa-debugger.chatmode.md"
    
  quality_gates:
    pre_delegation: "Context validation and complexity scoring"
    during_delegation: "Continuous quality monitoring"
    post_delegation: "Results validation and integration"