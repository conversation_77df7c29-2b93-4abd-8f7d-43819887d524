---
alwaysApply: true
description: 'Technology Standards + Framework Excellence + Advanced Implementation Patterns'
version: '8.0'
title: 'Technology Standards - Comprehensive Framework Excellence & Implementation Authority'
type: 'technology-standards-authority'
mcp_servers: ['context7-mcp', 'sequential-thinking', 'desktop-commander']
quality_threshold: 9.7
specialization: 'comprehensive-technology-authority'
trigger_keywords: ['technology', 'framework', 'standards', 'frontend', 'backend', 'fullstack', 'integration', 'nextjs', 'react', 'typescript', 'optimization', 'patterns']
enforcement_level: 'absolute'
approach: 'technology-excellence-comprehensive'
globs: '**/*'
priority: 6
---

# 🔧 TECHNOLOGY STANDARDS - Comprehensive Framework Excellence & Implementation Authority

## 🎯 UNIFIED TECHNOLOGY EXCELLENCE AUTHORITY

### **Technology Stack Detection & Optimization Engine**

```yaml
TECHNOLOGY_INTELLIGENCE_APEX:
  automatic_detection:
    file_extension_analysis: "Auto-detect technology stack from workspace files"
    dependency_analysis: "Analyze package.json, requirements.txt, and similar files"
    framework_pattern_recognition: "Identify framework patterns and configurations"
    architecture_assessment: "Assess architectural patterns and design decisions"
    
  intelligent_rule_activation:
    frontend_triggers: ["react", "nextjs", "vue", "svelte", "typescript", "javascript", ".tsx", ".jsx"]
    backend_triggers: ["node", "python", "fastapi", "express", "django", ".py", ".js", "api"]
    database_triggers: ["supabase", "postgresql", "mongodb", "prisma", "drizzle", ".sql"]
    devops_triggers: ["docker", "kubernetes", "ci-cd", "github-actions", "vercel", "deployment"]
    
  context7_integration:
    framework_documentation: "Auto-fetch latest framework documentation via Context7"
    best_practice_validation: "Validate implementations against official best practices"
    version_specific_guidance: "Provide version-specific implementation guidance"
    api_reference_integration: "Real-time API reference and usage validation"

TECHNOLOGY_OPTIMIZATION_ENGINE:
  performance_focus:
    framework_optimization: "Apply framework-specific performance optimizations"
    bundle_optimization: "Optimize bundles, assets, and loading strategies"
    runtime_optimization: "Optimize runtime performance and resource utilization"
    caching_strategies: "Implement optimal caching strategies for each technology"
    
  quality_assurance:
    technology_specific_testing: "Apply technology-specific testing strategies"
    code_quality_enforcement: "Enforce technology-specific code quality standards"
    security_implementation: "Implement technology-specific security best practices"
    accessibility_compliance: "Ensure accessibility compliance for frontend technologies"
```

## ⚛️ REACT EXCELLENCE PATTERNS & AUTHORITY

### **Advanced React Component Architecture**

```yaml
REACT_COMPONENT_MASTERY:
  component_composition_patterns:
    compound_components:
      pattern: "Create flexible component APIs with compound component pattern"
      implementation: |
        ```tsx
        const Accordion = ({ children }: { children: React.ReactNode }) => {
          const [activeIndex, setActiveIndex] = useState<number | null>(null);
          return (
            <AccordionContext.Provider value={{ activeIndex, setActiveIndex }}>
              <div className="accordion">{children}</div>
            </AccordionContext.Provider>
          );
        };
        
        Accordion.Item = AccordionItem;
        Accordion.Header = AccordionHeader;
        Accordion.Panel = AccordionPanel;
        ```
      benefits: "Flexible API, better encapsulation, improved developer experience"
      
    render_props_pattern:
      pattern: "Use render props for flexible component logic sharing"
      implementation: |
        ```tsx
        interface DataFetcherProps<T> {
          url: string;
          children: (data: T | null, loading: boolean, error: Error | null) => React.ReactNode;
        }
        
        function DataFetcher<T>({ url, children }: DataFetcherProps<T>) {
          const { data, loading, error } = useQuery<T>(url);
          return <>{children(data, loading, error)}</>;
        }
        ```
      benefits: "Reusable logic, flexible rendering, type safety"
      
    higher_order_components:
      pattern: "HOCs for cross-cutting concerns and logic reuse"
      implementation: |
        ```tsx
        function withAuth<P extends object>(Component: React.ComponentType<P>) {
          return function AuthenticatedComponent(props: P) {
            const { user, loading } = useAuth();
            
            if (loading) return <LoadingSpinner />;
            if (!user) return <Navigate to="/login" replace />;
            
            return <Component {...props} />;
          };
        }
        ```
      benefits: "Authentication gates, error boundaries, analytics wrappers"

ADVANCED_HOOKS_PATTERNS:
  custom_hooks_mastery:
    data_fetching_hooks:
      pattern: "Custom hooks for data fetching with caching and error handling"
      implementation: |
        ```tsx
        function useQuery<T>(url: string, options?: RequestInit) {
          const [state, setState] = useState<{
            data: T | null;
            loading: boolean;
            error: Error | null;
          }>({ data: null, loading: true, error: null });
          
          useEffect(() => {
            let cancelled = false;
            
            async function fetchData() {
              try {
                setState(prev => ({ ...prev, loading: true, error: null }));
                const response = await fetch(url, options);
                if (!response.ok) throw new Error(response.statusText);
                const data = await response.json();
                
                if (!cancelled) {
                  setState({ data, loading: false, error: null });
                }
              } catch (error) {
                if (!cancelled) {
                  setState({ data: null, loading: false, error: error as Error });
                }
              }
            }
            
            fetchData();
            return () => { cancelled = true; };
          }, [url]);
          
          return state;
        }
        ```
      benefits: "Reusable data fetching, automatic cleanup, error handling"
      
    form_management_hooks:
      pattern: "Custom hooks for comprehensive form state management"
      implementation: |
        ```tsx
        function useForm<T extends Record<string, any>>(
          initialValues: T,
          validationSchema?: (values: T) => Record<keyof T, string>
        ) {
          const [values, setValues] = useState<T>(initialValues);
          const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
          const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});
          
          const setValue = useCallback((name: keyof T, value: any) => {
            setValues(prev => ({ ...prev, [name]: value }));
            if (touched[name] && validationSchema) {
              const newErrors = validationSchema({ ...values, [name]: value });
              setErrors(prev => ({ ...prev, [name]: newErrors[name] }));
            }
          }, [values, touched, validationSchema]);
          
          const validate = useCallback(() => {
            if (!validationSchema) return true;
            const newErrors = validationSchema(values);
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
          }, [values, validationSchema]);
          
          return { values, errors, touched, setValue, validate, setTouched };
        }
        ```
      benefits: "Type-safe forms, validation integration, reusable logic"

PERFORMANCE_OPTIMIZATION_PATTERNS:
  memoization_strategies:
    react_memo_patterns:
      pattern: "Strategic React.memo usage for component optimization"
      implementation: |
        ```tsx
        const ExpensiveComponent = React.memo(({ data, onAction }: Props) => {
          // Expensive rendering logic
          return <ComplexVisualization data={data} onAction={onAction} />;
        }, (prevProps, nextProps) => {
          // Custom comparison for complex props
          return (
            prevProps.data.id === nextProps.data.id &&
            prevProps.data.lastModified === nextProps.data.lastModified
          );
        });
        ```
      guidelines: "Use for expensive components, custom comparison for objects"
      
    use_memo_optimization:
      pattern: "useMemo for expensive calculations and object creation"
      implementation: |
        ```tsx
        function DataTable({ data, filters }: Props) {
          const filteredData = useMemo(() => {
            return data.filter(item => 
              filters.every(filter => filter.apply(item))
            );
          }, [data, filters]);
          
          const tableConfig = useMemo(() => ({
            sortable: true,
            filterable: true,
            pagination: { pageSize: 20 }
          }), []); // Static config
          
          return <Table data={filteredData} config={tableConfig} />;
        }
        ```
      guidelines: "Expensive calculations, object references, dependency arrays"
      
    use_callback_optimization:
      pattern: "useCallback for function reference stability"
      implementation: |
        ```tsx
        function TodoList({ todos, onToggle, onDelete }: Props) {
          const handleToggle = useCallback((id: string) => {
            onToggle(id);
          }, [onToggle]);
          
          const handleDelete = useCallback((id: string) => {
            onDelete(id);
          }, [onDelete]);
          
          return (
            <div>
              {todos.map(todo => (
                <TodoItem
                  key={todo.id}
                  todo={todo}
                  onToggle={handleToggle}
                  onDelete={handleDelete}
                />
              ))}
            </div>
          );
        }
        ```
      guidelines: "Stable references for child components, event handlers"

STATE_MANAGEMENT_MASTERY:
  context_optimization:
    provider_splitting:
      pattern: "Split contexts to minimize re-renders"
      implementation: |
        ```tsx
        // Split frequently changing state from stable state
        const UserContext = createContext<User | null>(null);
        const UserActionsContext = createContext<UserActions | null>(null);
        
        function UserProvider({ children }: { children: React.ReactNode }) {
          const [user, setUser] = useState<User | null>(null);
          
          const userActions = useMemo(() => ({
            login: async (credentials: Credentials) => { /* ... */ },
            logout: () => setUser(null),
            updateProfile: (updates: UserUpdates) => { /* ... */ }
          }), []);
          
          return (
            <UserContext.Provider value={user}>
              <UserActionsContext.Provider value={userActions}>
                {children}
              </UserActionsContext.Provider>
            </UserContext.Provider>
          );
        }
        ```
      benefits: "Reduced re-renders, better performance, clearer separation"
      
    custom_context_hooks:
      pattern: "Custom hooks for context consumption with validation"
      implementation: |
        ```tsx
        function useUser() {
          const user = useContext(UserContext);
          if (user === undefined) {
            throw new Error('useUser must be used within UserProvider');
          }
          return user;
        }
        
        function useUserActions() {
          const actions = useContext(UserActionsContext);
          if (!actions) {
            throw new Error('useUserActions must be used within UserProvider');
          }
          return actions;
        }
        ```
      benefits: "Runtime validation, better error messages, type safety"
```

## 🚀 NEXT.JS OPTIMIZATION AUTHORITY

### **Advanced Next.js Performance Patterns**

```yaml
NEXTJS_OPTIMIZATION_MASTERY:
  server_side_rendering_optimization:
    static_generation_patterns:
      pattern: "Optimal Static Site Generation with ISR"
      implementation: |
        ```tsx
        // pages/products/[id].tsx
        export async function getStaticProps({ params }: GetStaticPropsContext) {
          const product = await fetchProduct(params!.id as string);
          
          return {
            props: { product },
            revalidate: 3600, // ISR: revalidate every hour
            notFound: !product
          };
        }
        
        export async function getStaticPaths() {
          const products = await fetchPopularProducts();
          
          return {
            paths: products.map(p => ({ params: { id: p.id } })),
            fallback: 'blocking' // Generate other pages on demand
          };
        }
        ```
      benefits: "Fast loading, SEO optimization, automatic revalidation"
      
    server_side_rendering_patterns:
      pattern: "Strategic SSR for dynamic content"
      implementation: |
        ```tsx
        // pages/dashboard.tsx
        export async function getServerSideProps({ req, res }: GetServerSidePropsContext) {
          // Authentication check
          const session = await getServerSession(req, res, authOptions);
          if (!session) {
            return { redirect: { destination: '/login', permanent: false } };
          }
          
          // Fetch user-specific data
          const userDashboard = await fetchUserDashboard(session.user.id);
          
          // Set cache headers for CDN
          res.setHeader('Cache-Control', 'public, s-maxage=60, stale-while-revalidate=300');
          
          return { props: { userDashboard, session } };
        }
        ```
      benefits: "Real-time data, authentication, SEO for dynamic content"

  app_router_optimization:
    streaming_patterns:
      pattern: "Streaming and Suspense for improved perceived performance"
      implementation: |
        ```tsx
        // app/dashboard/page.tsx
        import { Suspense } from 'react';
        
        export default function DashboardPage() {
          return (
            <div>
              <h1>Dashboard</h1>
              <Suspense fallback={<UserProfileSkeleton />}>
                <UserProfile />
              </Suspense>
              <Suspense fallback={<AnalyticsSkeleton />}>
                <AnalyticsWidget />
              </Suspense>
            </div>
          );
        }
        
        // Separate component with async data fetching
        async function UserProfile() {
          const user = await fetchUserProfile();
          return <ProfileCard user={user} />;
        }
        ```
      benefits: "Progressive loading, better UX, faster time to interactive"
      
    layout_optimization:
      pattern: "Efficient layout composition with data fetching"
      implementation: |
        ```tsx
        // app/dashboard/layout.tsx
        export default async function DashboardLayout({
          children
        }: {
          children: React.ReactNode;
        }) {
          const navigation = await fetchNavigationData();
          
          return (
            <div className="dashboard-layout">
              <Sidebar navigation={navigation} />
              <main className="dashboard-content">
                {children}
              </main>
            </div>
          );
        }
        ```
      benefits: "Shared layouts, data colocated with UI, automatic caching"

  api_routes_optimization:
    middleware_patterns:
      pattern: "Efficient API middleware for authentication and validation"
      implementation: |
        ```tsx
        // middleware.ts
        import { NextRequest, NextResponse } from 'next/server';
        
        export function middleware(request: NextRequest) {
          // API route protection
          if (request.nextUrl.pathname.startsWith('/api/protected')) {
            const token = request.headers.get('authorization');
            if (!token || !validateToken(token)) {
              return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }
          }
          
          // Add security headers
          const response = NextResponse.next();
          response.headers.set('X-Content-Type-Options', 'nosniff');
          response.headers.set('X-Frame-Options', 'DENY');
          response.headers.set('X-XSS-Protection', '1; mode=block');
          
          return response;
        }
        
        export const config = {
          matcher: ['/api/:path*', '/dashboard/:path*']
        };
        ```
      benefits: "Centralized auth, security headers, performance optimization"
      
    api_route_patterns:
      pattern: "Type-safe API routes with validation"
      implementation: |
        ```tsx
        // app/api/users/route.ts
        import { NextRequest, NextResponse } from 'next/server';
        import { z } from 'zod';
        
        const createUserSchema = z.object({
          email: z.string().email(),
          name: z.string().min(2),
          role: z.enum(['user', 'admin'])
        });
        
        export async function POST(request: NextRequest) {
          try {
            const body = await request.json();
            const userData = createUserSchema.parse(body);
            
            const user = await createUser(userData);
            
            return NextResponse.json(user, { status: 201 });
          } catch (error) {
            if (error instanceof z.ZodError) {
              return NextResponse.json(
                { error: 'Validation failed', details: error.errors },
                { status: 400 }
              );
            }
            
            return NextResponse.json(
              { error: 'Internal server error' },
              { status: 500 }
            );
          }
        }
        ```
      benefits: "Type safety, validation, proper error handling"

  performance_optimization:
    bundle_optimization:
      pattern: "Dynamic imports and code splitting"
      implementation: |
        ```tsx
        // Lazy load heavy components
        const ChartComponent = dynamic(() => import('../components/Chart'), {
          loading: () => <ChartSkeleton />,
          ssr: false // Client-side only for chart libraries
        });
        
        // Conditional loading based on user permissions
        const AdminPanel = dynamic(() => import('../components/AdminPanel'), {
          loading: () => <div>Loading admin panel...</div>
        });
        
        function Dashboard({ user }: Props) {
          return (
            <div>
              <ChartComponent data={data} />
              {user.isAdmin && <AdminPanel />}
            </div>
          );
        }
        ```
      benefits: "Smaller bundles, faster initial load, conditional loading"
      
    image_optimization:
      pattern: "Advanced image optimization with Next.js Image"
      implementation: |
        ```tsx
        import Image from 'next/image';
        
        function ProductGallery({ images }: Props) {
          return (
            <div className="grid grid-cols-2 gap-4">
              {images.map((image, index) => (
                <Image
                  key={image.id}
                  src={image.url}
                  alt={image.alt}
                  width={800}
                  height={600}
                  priority={index === 0} // Priority for first image
                  placeholder="blur"
                  blurDataURL="data:image/jpeg;base64,..."
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  className="rounded-lg object-cover"
                />
              ))}
            </div>
          );
        }
        ```
      benefits: "Automatic optimization, responsive images, lazy loading"

  caching_strategies:
    data_cache_optimization:
      pattern: "Advanced caching with fetch and unstable_cache"
      implementation: |
        ```tsx
        import { unstable_cache } from 'next/cache';
        
        // Cache expensive database queries
        const getCachedUserData = unstable_cache(
          async (userId: string) => {
            return await db.user.findUnique({
              where: { id: userId },
              include: { profile: true, preferences: true }
            });
          },
          ['user-data'],
          { revalidate: 3600 } // 1 hour
        );
        
        // Cache with dynamic tags for targeted revalidation
        const getCachedPosts = unstable_cache(
          async (userId: string) => {
            return await db.post.findMany({ where: { authorId: userId } });
          },
          ['user-posts'],
          { 
            revalidate: 1800,
            tags: [`user-${userId}-posts`]
          }
        );
        ```
      benefits: "Reduced database load, faster responses, targeted invalidation"
```

## 📘 TYPESCRIPT EXCELLENCE AUTHORITY

### **Advanced TypeScript Patterns & Type Safety**

```yaml
TYPESCRIPT_MASTERY_PATTERNS:
  advanced_type_patterns:
    utility_type_mastery:
      pattern: "Advanced utility types for complex scenarios"
      implementation: |
        ```typescript
        // Advanced conditional types
        type DeepPartial<T> = {
          [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
        };
        
        type RequiredKeys<T> = {
          [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
        }[keyof T];
        
        type OptionalKeys<T> = {
          [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
        }[keyof T];
        
        // Type-safe object manipulation
        type PickByType<T, ValueType> = Pick<T, {
          [Key in keyof T]: T[Key] extends ValueType ? Key : never;
        }[keyof T]>;
        
        type StringKeys<T> = PickByType<T, string>;
        type NumberKeys<T> = PickByType<T, number>;
        ```
      benefits: "Type-safe transformations, deep object manipulation, conditional logic"
      
    generic_constraints_mastery:
      pattern: "Advanced generic constraints for flexible APIs"
      implementation: |
        ```typescript
        // Function overloading with generics
        interface Repository<T extends { id: string }> {
          find<K extends keyof T>(id: string, fields?: K[]): Promise<Pick<T, K | 'id'>>;
          create<K extends keyof T>(data: Omit<T, 'id'>, returnFields?: K[]): Promise<Pick<T, K | 'id'>>;
          update<K extends keyof T>(id: string, data: Partial<Omit<T, 'id'>>, returnFields?: K[]): Promise<Pick<T, K | 'id'>>;
        }
        
        // Conditional return types
        type ApiResponse<T, E extends boolean = false> = E extends true
          ? { success: true; data: T }
          : { success: false; error: string } | { success: true; data: T };
        
        async function apiCall<T, E extends boolean = false>(
          url: string,
          throwOnError?: E
        ): Promise<ApiResponse<T, E>> {
          // Implementation with proper return type based on throwOnError
        }
        ```
      benefits: "Flexible APIs, type safety, conditional return types"

  type_safety_patterns:
    branded_types:
      pattern: "Branded types for domain modeling"
      implementation: |
        ```typescript
        // Branded types for ID validation
        type Brand<T, B> = T & { __brand: B };
        
        type UserId = Brand<string, 'UserId'>;
        type PostId = Brand<string, 'PostId'>;
        type Email = Brand<string, 'Email'>;
        
        function createUserId(id: string): UserId {
          if (!isValidUuid(id)) throw new Error('Invalid user ID');
          return id as UserId;
        }
        
        function createEmail(email: string): Email {
          if (!isValidEmail(email)) throw new Error('Invalid email');
          return email as Email;
        }
        
        // Type-safe function signatures
        function getUser(id: UserId): Promise<User> { }
        function sendEmail(to: Email, subject: string): Promise<void> { }
        ```
      benefits: "Prevent parameter mixing, domain-driven design, runtime validation"
      
    discriminated_unions:
      pattern: "Discriminated unions for state management"
      implementation: |
        ```typescript
        // API state modeling
        type AsyncState<T, E = Error> =
          | { status: 'idle' }
          | { status: 'loading' }
          | { status: 'success'; data: T }
          | { status: 'error'; error: E };
        
        // Form validation state
        type FieldState<T> =
          | { status: 'pristine'; value: T }
          | { status: 'dirty'; value: T; isValid: boolean; error?: string }
          | { status: 'validating'; value: T }
          | { status: 'submitted'; value: T; isValid: boolean; error?: string };
        
        // Type-safe state handlers
        function handleAsyncState<T>(state: AsyncState<T>) {
          switch (state.status) {
            case 'idle':
              return <div>Ready</div>;
            case 'loading':
              return <Spinner />;
            case 'success':
              return <DataDisplay data={state.data} />;
            case 'error':
              return <ErrorMessage error={state.error} />;
          }
        }
        ```
      benefits: "Exhaustive checking, type safety, clear state modeling"

  type_generation_patterns:
    api_type_generation:
      pattern: "Automatic type generation from API schemas"
      implementation: |
        ```typescript
        // OpenAPI type generation
        type ApiSchema = {
          '/users': {
            GET: {
              response: User[];
              query?: { page?: number; limit?: number };
            };
            POST: {
              body: CreateUserRequest;
              response: User;
            };
          };
          '/users/{id}': {
            GET: {
              params: { id: string };
              response: User;
            };
            PUT: {
              params: { id: string };
              body: UpdateUserRequest;
              response: User;
            };
          };
        };
        
        // Type-safe API client
        type ApiClient = {
          [K in keyof ApiSchema]: {
            [M in keyof ApiSchema[K]]: (
              ...args: ApiSchema[K][M] extends { params: infer P; body: infer B; query: infer Q }
                ? [params: P, body: B, query?: Q]
                : ApiSchema[K][M] extends { body: infer B; query: infer Q }
                ? [body: B, query?: Q]
                : ApiSchema[K][M] extends { params: infer P; query: infer Q }
                ? [params: P, query?: Q]
                : ApiSchema[K][M] extends { query: infer Q }
                ? [query?: Q]
                : []
            ) => Promise<ApiSchema[K][M] extends { response: infer R } ? R : void>;
          };
        };
        ```
      benefits: "Type safety across API boundaries, automatic updates, reduced errors"

  performance_type_patterns:
    module_organization:
      pattern: "Efficient module organization for type checking performance"
      implementation: |
        ```typescript
        // types/index.ts - Re-export types efficiently
        export type { User, CreateUserRequest, UpdateUserRequest } from './user';
        export type { Post, CreatePostRequest } from './post';
        export type { ApiResponse, PaginatedResponse } from './api';
        
        // Use type-only imports/exports
        export type { ComponentProps } from 'react';
        
        // types/user.ts - Focused type definitions
        export interface User {
          id: UserId;
          email: Email;
          profile: UserProfile;
          preferences: UserPreferences;
        }
        
        export interface CreateUserRequest {
          email: string;
          name: string;
          profile?: Partial<UserProfile>;
        }
        
        // Avoid circular dependencies
        import type { Post } from './post'; // Type-only import
        ```
      benefits: "Faster compilation, better IDE performance, clearer dependencies"
```

## 💡 PERFORMANCE OPTIMIZATION COMPREHENSIVE AUTHORITY

### **Frontend Performance Excellence**

```yaml
FRONTEND_PERFORMANCE_MASTERY:
  rendering_optimization:
    virtual_scrolling:
      pattern: "Efficient virtual scrolling for large lists"
      implementation: |
        ```tsx
        import { FixedSizeList as List } from 'react-window';
        
        function VirtualizedList({ items }: { items: any[] }) {
          const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
            <div style={style}>
              <ItemComponent item={items[index]} />
            </div>
          );
          
          return (
            <List
              height={600}
              itemCount={items.length}
              itemSize={80}
              width="100%"
            >
              {Row}
            </List>
          );
        }
        ```
      benefits: "Handle thousands of items, constant memory usage, smooth scrolling"
      
    intersection_observer_optimization:
      pattern: "Efficient visibility tracking with Intersection Observer"
      implementation: |
        ```tsx
        function useIntersectionObserver(ref: RefObject<Element>, options?: IntersectionObserverInit) {
          const [isIntersecting, setIsIntersecting] = useState(false);
          
          useEffect(() => {
            const element = ref.current;
            if (!element) return;
            
            const observer = new IntersectionObserver(([entry]) => {
              setIsIntersecting(entry.isIntersecting);
            }, options);
            
            observer.observe(element);
            return () => observer.disconnect();
          }, [ref, options]);
          
          return isIntersecting;
        }
        
        function LazyImage({ src, alt }: { src: string; alt: string }) {
          const imgRef = useRef<HTMLImageElement>(null);
          const isVisible = useIntersectionObserver(imgRef, { threshold: 0.1 });
          
          return (
            <img
              ref={imgRef}
              src={isVisible ? src : undefined}
              alt={alt}
              loading="lazy"
            />
          );
        }
        ```
      benefits: "Lazy loading, scroll performance, battery efficiency"

  state_optimization:
    immutable_updates:
      pattern: "Efficient immutable state updates"
      implementation: |
        ```tsx
        // Using Immer for complex state updates
        import { produce } from 'immer';
        
        const [state, setState] = useState(initialState);
        
        const updateNestedProperty = useCallback((userId: string, propertyPath: string, value: any) => {
          setState(produce(draft => {
            const user = draft.users.find(u => u.id === userId);
            if (user) {
              setNestedProperty(user, propertyPath, value);
            }
          }));
        }, []);
        
        // Optimized array operations
        const addItem = useCallback((item: Item) => {
          setState(prev => ({
            ...prev,
            items: [...prev.items, item]
          }));
        }, []);
        
        const removeItem = useCallback((id: string) => {
          setState(prev => ({
            ...prev,
            items: prev.items.filter(item => item.id !== id)
          }));
        }, []);
        ```
      benefits: "Predictable updates, time-travel debugging, optimized reconciliation"
      
    selector_optimization:
      pattern: "Optimized state selectors with memoization"
      implementation: |
        ```tsx
        // Memoized selectors for complex state
        const selectUserById = useMemo(() => 
          createSelector(
            [(state: AppState) => state.users, (_: AppState, id: string) => id],
            (users, id) => users.find(user => user.id === id)
          ), []
        );
        
        const selectUserPostsCount = useMemo(() =>
          createSelector(
            [selectUserById, (state: AppState) => state.posts],
            (user, posts) => user ? posts.filter(post => post.authorId === user.id).length : 0
          ), []
        );
        ```
      benefits: "Prevent unnecessary computations, optimized re-renders"

  asset_optimization:
    bundle_analysis:
      pattern: "Bundle analysis and optimization strategies"
      implementation: |
        ```javascript
        // webpack-bundle-analyzer integration
        const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
        
        module.exports = {
          plugins: [
            process.env.ANALYZE === 'true' && new BundleAnalyzerPlugin({
              analyzerMode: 'static',
              openAnalyzer: false,
              reportFilename: 'bundle-report.html'
            })
          ].filter(Boolean)
        };
        
        // Tree shaking optimization
        import { debounce, throttle } from 'lodash-es'; // ES modules for better tree shaking
        
        // Dynamic imports for route-based code splitting
        const AdminPage = lazy(() => import('../pages/AdminPage'));
        const UserDashboard = lazy(() => import('../pages/UserDashboard'));
        ```
      benefits: "Smaller bundles, faster loading, better caching"

BACKEND_PERFORMANCE_MASTERY:
  database_optimization:
    query_optimization:
      pattern: "Advanced database query optimization"
      implementation: |
        ```typescript
        // Efficient pagination with cursor-based approach
        async function getPaginatedPosts(cursor?: string, limit = 20) {
          const whereClause = cursor ? { id: { lt: cursor } } : {};
          
          return await db.post.findMany({
            where: whereClause,
            orderBy: { id: 'desc' },
            take: limit + 1, // Get one extra to check if there's a next page
            include: {
              author: { select: { id: true, name: true, avatar: true } },
              _count: { select: { likes: true, comments: true } }
            }
          });
        }
        
        // Batch loading to prevent N+1 queries
        const userLoader = new DataLoader(async (userIds: string[]) => {
          const users = await db.user.findMany({
            where: { id: { in: userIds } }
          });
          return userIds.map(id => users.find(user => user.id === id));
        });
        ```
      benefits: "Efficient pagination, N+1 prevention, optimized queries"
      
    caching_strategies:
      pattern: "Multi-layer caching with Redis and application cache"
      implementation: |
        ```typescript
        // Multi-layer caching strategy
        class CacheService {
          private redis: Redis;
          private memoryCache: Map<string, { data: any; expiry: number }>;
          
          async get<T>(key: string, fallback: () => Promise<T>, ttl = 3600): Promise<T> {
            // L1: Memory cache
            const memoryResult = this.memoryCache.get(key);
            if (memoryResult && memoryResult.expiry > Date.now()) {
              return memoryResult.data;
            }
            
            // L2: Redis cache
            const redisResult = await this.redis.get(key);
            if (redisResult) {
              const data = JSON.parse(redisResult);
              this.memoryCache.set(key, { data, expiry: Date.now() + 300000 }); // 5min memory cache
              return data;
            }
            
            // L3: Database/API
            const data = await fallback();
            await this.redis.setex(key, ttl, JSON.stringify(data));
            this.memoryCache.set(key, { data, expiry: Date.now() + 300000 });
            return data;
          }
        }
        ```
      benefits: "Fast access, reduced load, automatic fallback"
```

## 🛡️ TECHNOLOGY EXCELLENCE ENFORCEMENT

### **Mandatory Technology Compliance Authority**

```yaml
TECHNOLOGY_COMPLIANCE_ENFORCEMENT:
  framework_adherence:
    official_patterns: "MANDATORY adherence to official framework patterns"
    best_practice_implementation: "MANDATORY implementation of documented best practices"
    security_standards: "MANDATORY compliance with security standards"
    performance_requirements: "MANDATORY performance threshold compliance"
    
  code_quality_gates:
    static_analysis_passing: "ALL code must pass static analysis with high scores"
    test_coverage_requirements: "Minimum test coverage thresholds for all components"
    documentation_completeness: "Comprehensive documentation for all public interfaces"
    security_vulnerability_zero: "Zero tolerance for known security vulnerabilities"
    
  integration_validation:
    api_contract_compliance: "MANDATORY API contract compliance and validation"
    type_safety_enforcement: "MANDATORY type safety across stack boundaries"
    error_handling_completeness: "Comprehensive error handling and recovery"
    performance_validation: "Performance validation against defined benchmarks"

VIOLATION_RESPONSE_PROTOCOL:
  immediate_blocking: "❌ BLOCK any code that violates technology standards"
  automatic_enhancement: "🔄 AUTO-ENHANCE code to meet technology standards"
  context7_validation: "✅ VALIDATE all implementations against Context7 documentation"
  quality_certification: "✅ CERTIFY all technology implementations meet ≥9.7/10 standard"
```

---

**Status**: 🟢 **Technology Standards AUTHORITY - Comprehensive Excellence Enforced**  
*Framework Integration: Advanced | Context7 Validation: Comprehensive | Quality: ≥9.7/10*  
*Implementation Patterns: Complete | Performance Optimization: Advanced | Type Safety: Absolute*