#!/usr/bin/env python3
"""
VIBECODE Modular System Validation Script
Execute complete system validation following modular-validation-suite.md
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime

class VIBECODEModularValidator:
    def __init__(self):
        self.workspace_root = Path("C:/Users/<USER>/OneDrive/GRUPOUS/VSCODE")
        self.validation_results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "PENDING",
            "validation_score": 0,
            "components": {},
            "critical_tests": {},
            "performance_metrics": {},
            "errors": []
        }
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def validate_file_exists(self, file_path):
        """Check if a file exists and is readable"""
        full_path = self.workspace_root / file_path
        return full_path.exists() and full_path.is_file()
        
    def validate_directory_exists(self, dir_path):
        """Check if a directory exists"""
        full_path = self.workspace_root / dir_path
        return full_path.exists() and full_path.is_dir()

    def component_validation_1_core_rules(self):
        """1. Core Rule Loading Validation"""
        self.log("🔍 Validating Core Rule Loading...")
        
        core_files = [
            ".github/rules/core/base-instructions.md",
            ".github/rules/core/code-quality.md", 
            ".github/rules/core/security-baseline.md",
            ".github/rules/core/mcp-enforcement.md",
            ".github/rules/core/initialization-protocols.md"
        ]
        
        missing_files = []
        for file in core_files:
            if not self.validate_file_exists(file):
                missing_files.append(file)
                
        enhanced_workflow = ".github/rules/workflows/enhanced-workflow-execution.md"
        if not self.validate_file_exists(enhanced_workflow):
            missing_files.append(enhanced_workflow)
            
        score = 100 - (len(missing_files) * 15)
        self.validation_results["components"]["core_rules"] = {
            "score": max(0, score),
            "status": "PASS" if score >= 85 else "FAIL",
            "missing_files": missing_files,
            "details": f"Core rules validation: {len(core_files) + 1 - len(missing_files)}/{len(core_files) + 1} files found"
        }
        
        if missing_files:
            self.log(f"❌ Missing core files: {', '.join(missing_files)}", "ERROR")
        else:
            self.log("✅ All core rule files present", "SUCCESS")
            
        return score >= 85

    def component_validation_2_enhanced_workflow(self):
        """2. Enhanced Workflow Validation"""
        self.log("🔍 Validating Enhanced Workflow...")
        
        workflow_file = ".github/rules/workflows/enhanced-workflow-execution.md"
        validation_file = ".github/docs/modular-validation-suite.md"
        
        files_exist = self.validate_file_exists(workflow_file) and self.validate_file_exists(validation_file)
        
        # Check if files have expected content structure
        content_valid = True
        if files_exist:
            try:
                with open(self.workspace_root / workflow_file, 'r', encoding='utf-8') as f:
                    workflow_content = f.read()
                    if "7-step" not in workflow_content.lower() or "persistence" not in workflow_content.lower():
                        content_valid = False
                        
                with open(self.workspace_root / validation_file, 'r', encoding='utf-8') as f:
                    validation_content = f.read()
                    if "Component Validation" not in validation_content or "Critical Validation" not in validation_content:
                        content_valid = False
            except Exception as e:
                content_valid = False
                self.validation_results["errors"].append(f"Error reading workflow files: {str(e)}")
        
        score = 100 if files_exist and content_valid else 50 if files_exist else 0
        
        self.validation_results["components"]["enhanced_workflow"] = {
            "score": score,
            "status": "PASS" if score >= 85 else "FAIL",
            "files_exist": files_exist,
            "content_valid": content_valid,
            "details": f"Enhanced workflow validation: Files exist: {files_exist}, Content valid: {content_valid}"
        }
        
        if score >= 85:
            self.log("✅ Enhanced workflow validation passed", "SUCCESS")
        else:
            self.log("❌ Enhanced workflow validation failed", "ERROR")
            
        return score >= 85

    def component_validation_3_agent_chatmode(self):
        """3. Agent Chatmode Validation"""
        self.log("🔍 Validating Agent Chatmode...")
        
        voidbeast_file = ".github/chatmodes/voidbeast-modular.chatmode.md"
        
        if not self.validate_file_exists(voidbeast_file):
            score = 0
        else:
            try:
                with open(self.workspace_root / voidbeast_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                required_features = [
                    "MODE_CLASSIFICATION",
                    "enhanced-workflow-execution.md",
                    "modular-validation-suite.md", 
                    "RESEARCH_MODE",
                    "PLAN_MODE",
                    "ACT_MODE",
                    "≥9.5/10"
                ]
                
                features_found = sum(1 for feature in required_features if feature in content)
                score = (features_found / len(required_features)) * 100
                
            except Exception as e:
                score = 0
                self.validation_results["errors"].append(f"Error reading VoidBeast chatmode: {str(e)}")
        
        self.validation_results["components"]["agent_chatmode"] = {
            "score": score,
            "status": "PASS" if score >= 85 else "FAIL",
            "features_found": features_found if 'features_found' in locals() else 0,
            "details": f"VoidBeast chatmode validation: {score:.1f}% feature coverage"
        }
        
        if score >= 85:
            self.log("✅ Agent chatmode validation passed", "SUCCESS")
        else:
            self.log("❌ Agent chatmode validation failed", "ERROR")
            
        return score >= 85

    def component_validation_4_context_optimization(self):
        """4. Context Optimization Validation"""
        self.log("🔍 Validating Context Optimization...")
        
        copilot_instructions = ".github/copilot-instructions.md"
        rules_structure = [
            ".github/rules/core/",
            ".github/rules/workflows/", 
            ".github/rules/context/",
            ".github/rules/agents/"
        ]
        
        # Check main copilot instructions file
        instructions_exist = self.validate_file_exists(copilot_instructions)
        
        # Check modular rule structure
        structure_score = 0
        for rule_dir in rules_structure:
            if self.validate_directory_exists(rule_dir):
                structure_score += 25
                
        # Check for intelligent loading references
        loading_intelligence = False
        if instructions_exist:
            try:
                with open(self.workspace_root / copilot_instructions, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "modular" in content.lower() and "context" in content.lower():
                        loading_intelligence = True
            except Exception as e:
                self.validation_results["errors"].append(f"Error reading copilot instructions: {str(e)}")
        
        total_score = (structure_score + (50 if instructions_exist else 0) + (25 if loading_intelligence else 0))
        
        self.validation_results["components"]["context_optimization"] = {
            "score": min(100, total_score),
            "status": "PASS" if total_score >= 85 else "FAIL",
            "structure_score": structure_score,
            "instructions_exist": instructions_exist,
            "loading_intelligence": loading_intelligence,
            "details": f"Context optimization: Structure: {structure_score}%, Instructions: {instructions_exist}, Intelligence: {loading_intelligence}"
        }
        
        if total_score >= 85:
            self.log("✅ Context optimization validation passed", "SUCCESS")
        else:
            self.log("❌ Context optimization validation failed", "ERROR")
            
        return total_score >= 85

    def critical_test_1_mcp_enforcement(self):
        """Critical Test 1: MCP Enforcement Test"""
        self.log("🚨 Testing MCP Enforcement...")
        
        # This would normally test actual MCP bypassing - for validation we check configuration
        mcp_config_files = [
            ".github/rules/core/mcp-enforcement.md",
            ".claude/mcp.json"
        ]
        
        mcp_configured = all(self.validate_file_exists(f) for f in mcp_config_files)
        
        # Check for enforcement keywords in voidbeast
        enforcement_active = False
        voidbeast_file = ".github/chatmodes/voidbeast-modular.chatmode.md"
        if self.validate_file_exists(voidbeast_file):
            try:
                with open(self.workspace_root / voidbeast_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "ZERO TOLERANCE" in content and "MCP" in content:
                        enforcement_active = True
            except Exception as e:
                self.validation_results["errors"].append(f"Error checking MCP enforcement: {str(e)}")
        
        score = 100 if mcp_configured and enforcement_active else 50 if mcp_configured else 0
        
        self.validation_results["critical_tests"]["mcp_enforcement"] = {
            "score": score,
            "status": "PASS" if score >= 85 else "FAIL",
            "mcp_configured": mcp_configured,
            "enforcement_active": enforcement_active,
            "details": f"MCP enforcement test: Configuration: {mcp_configured}, Active enforcement: {enforcement_active}"
        }
        
        if score >= 85:
            self.log("✅ MCP enforcement test passed", "SUCCESS")
        else:
            self.log("❌ MCP enforcement test failed", "ERROR")
            
        return score >= 85

    def critical_test_2_quality_threshold(self):
        """Critical Test 2: Quality Threshold Test"""
        self.log("🚨 Testing Quality Threshold...")
        
        # Check if quality thresholds are properly configured
        quality_files = [
            ".github/rules/core/code-quality.md",
            ".github/chatmodes/voidbeast-modular.chatmode.md"
        ]
        
        threshold_configured = False
        for file in quality_files:
            if self.validate_file_exists(file):
                try:
                    with open(self.workspace_root / file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if "9.5/10" in content or "≥9.5" in content:
                            threshold_configured = True
                            break
                except Exception as e:
                    self.validation_results["errors"].append(f"Error checking quality threshold in {file}: {str(e)}")
        
        score = 100 if threshold_configured else 0
        
        self.validation_results["critical_tests"]["quality_threshold"] = {
            "score": score,
            "status": "PASS" if score >= 85 else "FAIL",
            "threshold_configured": threshold_configured,
            "details": f"Quality threshold test: ≥9.5/10 threshold configured: {threshold_configured}"
        }
        
        if score >= 85:
            self.log("✅ Quality threshold test passed", "SUCCESS")
        else:
            self.log("❌ Quality threshold test failed", "ERROR")
            
        return score >= 85

    def performance_benchmark_context_loading(self):
        """Performance Benchmark: Context Loading Efficiency"""
        self.log("📊 Measuring Context Loading Performance...")
        
        start_time = time.time()
        
        # Simulate context loading by checking file access times
        test_files = [
            ".github/copilot-instructions.md",
            ".github/rules/workflows/enhanced-workflow-execution.md",
            ".github/docs/modular-validation-suite.md",
            ".github/chatmodes/voidbeast-modular.chatmode.md"
        ]
        
        loading_times = []
        files_accessible = 0
        
        for file in test_files:
            file_start = time.time()
            if self.validate_file_exists(file):
                files_accessible += 1
                try:
                    with open(self.workspace_root / file, 'r', encoding='utf-8') as f:
                        _ = len(f.read())  # Simulate loading
                except:
                    pass
            file_time = (time.time() - file_start) * 1000  # Convert to ms
            loading_times.append(file_time)
        
        avg_loading_time = sum(loading_times) / len(loading_times) if loading_times else float('inf')
        total_time = (time.time() - start_time) * 1000
        
        # Performance scoring
        time_score = 100 if avg_loading_time < 200 else max(0, 100 - (avg_loading_time - 200) / 10)
        access_score = (files_accessible / len(test_files)) * 100
        
        performance_score = (time_score + access_score) / 2
        
        self.validation_results["performance_metrics"]["context_loading"] = {
            "score": performance_score,
            "avg_loading_time_ms": round(avg_loading_time, 2),
            "total_time_ms": round(total_time, 2),
            "files_accessible": files_accessible,
            "target_time_ms": 200,
            "status": "PASS" if performance_score >= 85 else "FAIL",
            "details": f"Context loading: {avg_loading_time:.1f}ms avg, {files_accessible}/{len(test_files)} files accessible"
        }
        
        if performance_score >= 85:
            self.log(f"✅ Context loading performance passed: {avg_loading_time:.1f}ms", "SUCCESS")
        else:
            self.log(f"❌ Context loading performance failed: {avg_loading_time:.1f}ms", "ERROR")
            
        return performance_score >= 85

    def run_full_validation(self):
        """Execute complete system validation"""
        self.log("🚀 Starting VIBECODE Modular System Validation...")
        self.log("=" * 60)
        
        # Component validations
        component_tests = [
            self.component_validation_1_core_rules,
            self.component_validation_2_enhanced_workflow,
            self.component_validation_3_agent_chatmode,
            self.component_validation_4_context_optimization
        ]
        
        # Critical tests
        critical_tests = [
            self.critical_test_1_mcp_enforcement,
            self.critical_test_2_quality_threshold
        ]
        
        # Performance benchmarks
        performance_tests = [
            self.performance_benchmark_context_loading
        ]
        
        # Execute all tests
        component_results = [test() for test in component_tests]
        critical_results = [test() for test in critical_tests]
        performance_results = [test() for test in performance_tests]
        
        # Calculate overall score
        all_scores = []
        for category in ["components", "critical_tests", "performance_metrics"]:
            for test_name, result in self.validation_results[category].items():
                all_scores.append(result["score"])
        
        overall_score = sum(all_scores) / len(all_scores) if all_scores else 0
        self.validation_results["validation_score"] = round(overall_score, 2)
        
        # Determine overall status
        if overall_score >= 95:
            self.validation_results["overall_status"] = "EXCELLENT"
        elif overall_score >= 85:
            self.validation_results["overall_status"] = "PASS"
        elif overall_score >= 70:
            self.validation_results["overall_status"] = "WARNING"
        else:
            self.validation_results["overall_status"] = "FAIL"
        
        # Generate final report
        self.generate_validation_report()
        
        return self.validation_results["overall_status"] in ["EXCELLENT", "PASS"]

    def generate_validation_report(self):
        """Generate comprehensive validation report"""
        self.log("=" * 60)
        self.log("📋 VALIDATION REPORT")
        self.log("=" * 60)
        
        status = self.validation_results["overall_status"]
        score = self.validation_results["validation_score"]
        
        status_emoji = {
            "EXCELLENT": "🏆",
            "PASS": "✅", 
            "WARNING": "⚠️",
            "FAIL": "❌"
        }
        
        self.log(f"{status_emoji.get(status, '❓')} Overall Status: {status}")
        self.log(f"📊 Overall Score: {score:.1f}/100")
        self.log("")
        
        # Component results
        self.log("🔧 Component Validations:")
        for test_name, result in self.validation_results["components"].items():
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            self.log(f"  {status_icon} {test_name}: {result['score']:.1f}/100 - {result['details']}")
        
        # Critical test results  
        self.log("\n🚨 Critical Tests:")
        for test_name, result in self.validation_results["critical_tests"].items():
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            self.log(f"  {status_icon} {test_name}: {result['score']:.1f}/100 - {result['details']}")
        
        # Performance results
        self.log("\n📊 Performance Metrics:")
        for test_name, result in self.validation_results["performance_metrics"].items():
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            self.log(f"  {status_icon} {test_name}: {result['score']:.1f}/100 - {result['details']}")
        
        # Errors
        if self.validation_results["errors"]:
            self.log("\n🔥 Errors Encountered:")
            for error in self.validation_results["errors"]:
                self.log(f"  ❌ {error}")
        
        self.log("=" * 60)
        
        # Save detailed results to file
        report_file = self.workspace_root / ".github" / "docs" / "validation-results.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.validation_results, f, indent=2, ensure_ascii=False)
            self.log(f"📄 Detailed results saved to: {report_file}")
        except Exception as e:
            self.log(f"❌ Failed to save results: {str(e)}", "ERROR")

if __name__ == "__main__":
    validator = VIBECODEModularValidator()
    success = validator.run_full_validation()
    sys.exit(0 if success else 1)