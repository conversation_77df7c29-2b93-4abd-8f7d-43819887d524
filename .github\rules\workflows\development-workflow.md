# 🔄 VIBECODE V6.0 - Development Workflow Rules (Modular)

**ACTIVATION**: Trigger-based loading for development and implementation tasks  
**ORCHESTRATION**: Coordinated between copilot-instructions.md ↔ voidbeast-modular.chatmode.md  
**INTEGRATION**: GitHub Copilot native + specialized chatmode delegation  
**QUALITY**: ≥9.6/10 for development workflows  

## 🎯 DEVELOPMENT WORKFLOW ACTIVATION

### **Trigger-Based Workflow Rules**
```yaml
DEVELOPMENT_WORKFLOW_TRIGGERS:
  activation_keywords: ["code", "implement", "build", "develop", "create", "fix", "desenvolver", "construir", "implementar"]
  complexity_threshold: 3.5
  orchestrator_coordination: "Hub → VoidBeast → Specialized (based on complexity)"
  quality_threshold: 9.6
  
WORKFLOW_ROUTING:
  L1_simple_development:
    range: "1.0-3.0"
    handler: "copilot-instructions.md (hub direct)"
    examples: ["Simple code fixes", "Basic implementations", "Standard patterns"]
    
  L2_moderate_development:
    range: "3.1-5.5"
    handler: "voidbeast-modular.chatmode.md → specialized chatmode"
    examples: ["Feature implementation", "Component creation", "API integration"]
    
  L3_complex_development:
    range: "5.6-7.5"
    handler: "voidbeast-modular.chatmode.md → APEX specialists"
    examples: ["System integration", "Architecture implementation", "Complex features"]
    
  L4_enterprise_development:
    range: "7.6-10.0"
    handler: "Full APEX orchestration"
    examples: ["Multi-system integration", "Enterprise architecture", "Complex orchestration"]
```

## 🚀 DEVELOPMENT STANDARDS & PATTERNS

### **Code Quality & Implementation Standards**
```yaml
DEVELOPMENT_STANDARDS:
  typescript_requirements:
    - "Always use TypeScript for type safety"
    - "Define interfaces for all data structures"
    - "Use strict type checking"
    - "Implement proper error handling"
    
  react_nextjs_patterns:
    - "Use functional components with hooks"
    - "Follow React best practices"
    - "Implement proper state management"
    - "Use Next.js optimizations"
    
  supabase_integration:
    - "Mandatory RLS (Row Level Security) for all tables"
    - "Use Supabase client properly"
    - "Implement proper authentication"
    - "Follow database best practices"
    
  performance_requirements:
    - "Optimize for Core Web Vitals"
    - "Implement proper caching strategies"
    - "Use code splitting and lazy loading"
    - "Monitor performance metrics"
```

## 🔧 MCP INTEGRATION FOR DEVELOPMENT

### **Development-Specific MCP Usage**
```yaml
DEVELOPMENT_MCP_PROTOCOLS:
  context7_documentation:
    usage: "ALWAYS for technical documentation and API references"
    focus: "Official docs, best practices, current API specifications"
    timing: "Before any implementation begins"
    
  sequential_thinking:
    usage: "For complex implementation planning"
    focus: "Step-by-step implementation breakdown"
    timing: "During planning and complex problem solving"
    
  desktop_commander:
    usage: "ALL file operations and project management"
    focus: "File creation, directory management, project setup"
    timing: "Throughout implementation process"
    
  research_enhancement:
    complexity_3_5: "context7 + sequential-thinking"
    complexity_5_6: "context7 + tavily + sequential-thinking"
    complexity_7_10: "Full 3-MCP research chain"
```

## 🎛️ SPECIALIZED CHATMODE COORDINATION

### **Development Domain Delegation**
```yaml
DEVELOPMENT_DELEGATION:
  neonpro_development:
    triggers: ["neonpro", "clínica", "saúde", "paciente", "medical"]
    specialization: "Healthcare domain development"
    quality_threshold: 9.7
    
  bmad_implementation:
    triggers: ["story", "task", "epic", "implementar task", "desenvolver story"]
    specialization: "BMad Method story-driven development"
    quality_threshold: 9.6
    
  apex_architecture:
    triggers: ["architecture", "design", "system", "structure"]
    specialization: "Complex architecture implementation"
    quality_threshold: 9.8
    
  performance_optimization:
    triggers: ["optimize", "performance", "speed", "efficiency"]
    specialization: "Performance-critical implementations"
    quality_threshold: 9.7
```

## 🛡️ QUALITY GATES & VALIDATION

### **Development Quality Enforcement**
```yaml
DEVELOPMENT_QUALITY_GATES:
  pre_implementation:
    - "Validate requirements and technical approach"
    - "Ensure proper architecture and design patterns"
    - "Verify technology stack compatibility"
    - "Assess complexity and resource requirements"
    
  during_implementation:
    - "Monitor code quality and standards compliance"
    - "Validate security and performance considerations"
    - "Ensure proper error handling and edge cases"
    - "Verify integration with existing systems"
    
  post_implementation:
    - "Comprehensive testing and validation"
    - "Performance benchmarking and optimization"
    - "Security audit and compliance verification"
    - "Documentation and knowledge transfer"
    
QUALITY_METRICS:
  code_quality: "≥9.6/10 using industry standards"
  performance: "Core Web Vitals compliance"
  security: "Security best practices implementation"
  maintainability: "Clean, readable, documented code"
  testing: "Comprehensive test coverage"
```

---

## 🎯 STATUS: DEVELOPMENT WORKFLOW RULES ACTIVE

**Activation**: 🟢 **TRIGGER-BASED** - Development keyword detection (≥3.5 complexity)  
**Integration**: Seamless orchestrator coordination + specialized delegation  
**Quality**: ≥9.6/10 development standards | GitHub Copilot enhanced  
**Coverage**: TypeScript, React, Next.js, Supabase, Performance optimization