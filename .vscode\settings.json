{"github.copilot.chat.codeGeneration.useInstructionFiles": true, "github.copilot.chat.codeGeneration.instructions": [{"text": "Always follow VIBECODE V6.0 modular orchestration protocols", "file": ".github/copilot-instructions.md"}], "chat.instructionsFilesLocations": [".github/instructions/mcp.instructions.md", ".github/instructions/memory-bank.instructions.md", ".github/instructions/performance-optimization.instructions.md", ".github/copilot-instructions.md"], "chat.modeFilesLocations": [".github/chatmodes/"], "chat.promptFilesLocations": {".github/prompts": true}, "chat.agent.enabled": true, "chat.agent.maxRequests": 50, "github.copilot.chat.agent.autoFix": true, "github.copilot.chat.agent.runTasks": true, "chat.implicitContext.enabled": true, "github.copilot.chat.edits.temporalContext.enabled": true, "chat.mcp.enabled": true, "editor.inlineSuggest.fontFamily": "Fira Code, Consolas, monospace", "editor.inlineSuggest.showToolbar": "always", "editor.inlineSuggest.syntaxHighlightingEnabled": true, "github.copilot.nextEditSuggestions.enabled": true, "editor.inlineSuggest.edits.allowCodeShifting": "always", "editor.inlineSuggest.edits.renderSideBySide": "auto", "github.copilot.enable": {"*": true, "yaml": true, "plaintext": true, "markdown": true}, "github.copilot.chat.followUps": true, "github.copilot.chat.scopeSelection": true, "github.copilot.chat.localeOverride": "pt-BR", "github.copilot.chat.useProjectContext": true, "github.copilot.chat.useFileContext": true, "github.copilot.chat.useGitContext": true, "github.copilot.chat.participants": {"@vscode": {"description": "VS Code and extension help with enhanced MCP integration", "enabled": true, "mcp_enhanced": true}, "@terminal": {"description": "Terminal and shell commands via Desktop Commander MCP", "enabled": true, "mcp_routing": "desktop_commander"}, "@workspace": {"description": "Workspace and project context with memory bank integration", "enabled": true, "memory_bank_integration": true}, "@research": {"description": "Research and documentation via Context7, <PERSON><PERSON>, Exa MCPs", "enabled": true, "mcp_chain": ["context7", "tavily", "exa"]}}, "github.copilot.chat.codeGeneration.languagePreferences": {"typescript": "prefer", "tsx": "prefer", "javascript": "avoid_unless_required", "jsx": "avoid_unless_required", "markdown": "mandatory_for_documentation", "sql": "use_supabase_client_only"}, "github.copilot.advanced": {}, "github.copilot.chat.quality": {"enableSyntaxCheck": true, "enableStyleCheck": true, "enableSecurityCheck": true, "enablePerformanceCheck": true, "enableAccessibilityCheck": true, "enableComplianceCheck": true, "qualityThreshold": 9.8, "autoEnhancement": true, "mcpValidation": true}, "workbench.colorTheme": "Default Dark+", "workbench.editor.showTabs": "single", "editor.minimap.enabled": false, "editor.fontSize": 14, "editor.fontFamily": "Consolas, 'Courier New', monospace", "terminal.integrated.defaultProfile.windows": "PowerShell", "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000}