{"version": "2.0.0", "tasks": [{"label": "NeonPro: Build Production", "type": "shell", "command": "pnpm", "args": ["build"], "options": {"cwd": "${workspaceFolder}/neonpro"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "NeonPro: Lint & Fix", "type": "shell", "command": "pnpm", "args": ["lint", "--fix"], "options": {"cwd": "${workspaceFolder}/neonpro"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "VIBECODE: System Health Check", "type": "shell", "command": "python", "args": ["memory-bank/python/operational_monitor.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "VIBECODE: Update Memory Bank", "type": "shell", "command": "python", "args": ["memory-bank/python/automatic_memory_updater.py"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "SaaS Projects: Build Shared", "type": "shell", "command": "npm", "args": ["run", "build:shared"], "options": {"cwd": "${workspaceFolder}/@saas-projects"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "Quick Setup: Install All Dependencies", "type": "shell", "command": "powershell", "args": ["-Command", "cd neonpro; pnpm install; cd ../@saas-projects; npm run install:shared"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Start Memory Bank V3.0 Sync", "type": "shell", "command": "python", "args": ["${workspaceFolder}/memory-bank/core/sync_manager.py", "--project-root", "${workspaceFolder}", "--daemon"], "group": "build", "presentation": {"echo": false, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": false}, "isBackground": true, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}, "detail": "Auto-starts VIBECODE V3.0 Memory Bank sync service when project opens"}, {"label": "Stop Memory Bank V3.0 Sync", "type": "shell", "command": "taskkill", "args": ["/F", "/IM", "python.exe", "/FI", "WINDOWTITLE eq *sync_manager*"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Stops VIBECODE V3.0 Memory Bank sync service"}, {"label": "Memory Bank V3.0 Status", "type": "shell", "command": "python", "args": ["${workspaceFolder}/memory-bank/core/sync_manager.py", "--project-root", "${workspaceFolder}"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "detail": "Check VIBECODE V3.0 Memory Bank sync status and perform one-time sync"}]}