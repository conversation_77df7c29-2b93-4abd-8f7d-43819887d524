#!/usr/bin/env node

/**
 * 🔧 VIBECODE V6.0 - Script de Aplicação e Validação de Configurações
 * Resolve L1 Configuration e valida integração GitHub Copilot
 */

const fs = require('fs').promises;
const path = require('path');

class ConfigurationValidator {
    constructor() {
        this.basePath = process.cwd();
        this.settingsPath = path.join(this.basePath, '.vscode', 'settings.json');
        this.errors = [];
        this.warnings = [];
        this.successes = [];
    }

    /**
     * 🔧 L1: Validação e Correção de Configurações
     */
    async validateAndFixConfigurations() {
        console.log('🔧 L1: VALIDAÇÃO E CORREÇÃO DE CONFIGURAÇÕES VS CODE\n');
        
        try {
            // Ler e validar settings.json
            const settingsContent = await fs.readFile(this.settingsPath, 'utf8');
            const settings = JSON.parse(settingsContent);
            
            // Configurações obrigatórias com valores corretos
            const requiredConfigs = {
                // GitHub Copilot Core
                'github.copilot.chat.codeGeneration.useInstructionFiles': true,
                'github.copilot.enable': { '*': true },
                'github.copilot.chat.localeOverride': 'pt-BR',
                
                // Chat & Context
                'chat.agent.enabled': true,
                'chat.agent.maxRequests': 50,
                'chat.mcp.enabled': true,
                'chat.implicitContext.enabled': true,
                
                // Locations
                'chat.instructionsFilesLocations': {
                    '.github/instructions': true,
                    '.github/rules/core': true,
                    '.github/rules/workflows': false
                },
                'chat.modeFilesLocations': {
                    '.github/chatmodes': true
                },
                
                // Editor Integration
                'github.copilot.nextEditSuggestions.enabled': true,
                'editor.inlineSuggest.showToolbar': 'always',
                'editor.inlineSuggest.syntaxHighlightingEnabled': true,
                'editor.inlineSuggest.edits.allowCodeShifting': 'always'
            };
            
            let needsUpdate = false;
            const updates = {};
            
            // Verificar e corrigir cada configuração
            for (const [key, expectedValue] of Object.entries(requiredConfigs)) {
                const currentValue = this.getNestedProperty(settings, key);
                
                if (JSON.stringify(currentValue) !== JSON.stringify(expectedValue)) {
                    this.warnings.push(`⚠️  ${key}: ${JSON.stringify(currentValue)} → ${JSON.stringify(expectedValue)}`);
                    this.setNestedProperty(updates, key, expectedValue);
                    needsUpdate = true;
                } else {
                    this.successes.push(`✅ ${key}: Configurado corretamente`);
                }
            }
            
            // Aplicar correções se necessário
            if (needsUpdate) {
                const mergedSettings = { ...settings, ...updates };
                await fs.writeFile(this.settingsPath, JSON.stringify(mergedSettings, null, 2));
                console.log('🔄 Configurações atualizadas em .vscode/settings.json\n');
            }
            
            // Validar arquivos de instrução
            await this.validateInstructionFiles();
            
            return !needsUpdate;
            
        } catch (error) {
            this.errors.push(`❌ Erro ao validar configurações: ${error.message}`);
            return false;
        }
    }

    /**
     * 📂 Validação de Arquivos de Instrução
     */
    async validateInstructionFiles() {
        console.log('📂 VALIDAÇÃO DE ARQUIVOS DE INSTRUÇÃO\n');
        
        const requiredFiles = [
            '.github/copilot-instructions.md',
            '.github/chatmodes/voidbeast-modular.chatmode.md',
            '.github/instructions/mcp.instructions.md',
            '.github/instructions/memory-bank.instructions.md'
        ];
        
        for (const filePath of requiredFiles) {
            const fullPath = path.join(this.basePath, filePath);
            try {
                await fs.access(fullPath);
                this.successes.push(`✅ ${filePath}: Arquivo existe`);
                
                // Verificar conteúdo VIBECODE V6.0
                const content = await fs.readFile(fullPath, 'utf8');
                if (content.includes('VIBECODE V6.0') || content.includes('VoidBeast V6.0')) {
                    this.successes.push(`✅ ${filePath}: Contém identificadores VIBECODE`);
                } else {
                    this.warnings.push(`⚠️  ${filePath}: Não contém identificadores VIBECODE`);
                }
                
            } catch (error) {
                this.errors.push(`❌ ${filePath}: Arquivo não encontrado`);
            }
        }
    }

    /**
     * 🎯 Teste de Integração GitHub Copilot
     */
    async testGitHubCopilotIntegration() {
        console.log('🎯 TESTE DE INTEGRAÇÃO GITHUB COPILOT\n');
        
        // Simular validações que o GitHub Copilot faria
        const integrationTests = [
            {
                name: 'Instruction Files Loading',
                check: () => this.checkFileExists('.github/copilot-instructions.md'),
                description: 'Verificar se instruction files são carregados'
            },
            {
                name: 'Chat Mode Files',
                check: () => this.checkFileExists('.github/chatmodes'),
                description: 'Verificar se chat modes estão disponíveis'
            },
            {
                name: 'MCP Integration',
                check: () => this.getNestedProperty(JSON.parse(require('fs').readFileSync(this.settingsPath, 'utf8')), 'chat.mcp.enabled'),
                description: 'Verificar se MCP está habilitado'
            },
            {
                name: 'Agent Mode',
                check: () => this.getNestedProperty(JSON.parse(require('fs').readFileSync(this.settingsPath, 'utf8')), 'chat.agent.enabled'),
                description: 'Verificar se Agent Mode está ativo'
            }
        ];
        
        for (const test of integrationTests) {
            try {
                const result = await test.check();
                if (result) {
                    this.successes.push(`✅ ${test.name}: ${test.description}`);
                } else {
                    this.warnings.push(`⚠️  ${test.name}: ${test.description} - Falhou`);
                }
            } catch (error) {
                this.errors.push(`❌ ${test.name}: ${error.message}`);
            }
        }
    }

    /**
     * 📋 Geração de Relatório de Configuração
     */
    generateConfigurationReport() {
        console.log('📋 RELATÓRIO DE CONFIGURAÇÃO - L1 RESOLUTION\n');
        console.log('='.repeat(80));
        
        console.log(`✅ SUCESSOS (${this.successes.length}):`);
        this.successes.forEach(success => console.log(`   ${success}`));
        
        if (this.warnings.length > 0) {
            console.log(`\n⚠️  AVISOS (${this.warnings.length}):`);
            this.warnings.forEach(warning => console.log(`   ${warning}`));
        }
        
        if (this.errors.length > 0) {
            console.log(`\n❌ ERROS (${this.errors.length}):`);
            this.errors.forEach(error => console.log(`   ${error}`));
        }
        
        const configurationHealth = this.successes.length / (this.successes.length + this.warnings.length + this.errors.length) * 100;
        
        console.log(`\n🎯 SAÚDE DA CONFIGURAÇÃO: ${configurationHealth.toFixed(1)}%`);
        
        if (configurationHealth >= 90) {
            console.log('🌟 EXCELENTE! Configurações aplicadas com sucesso.');
            console.log('\n📝 PRÓXIMOS PASSOS:');
            console.log('   1. Reiniciar VS Code (Ctrl+Shift+P → "Developer: Reload Window")');
            console.log('   2. Testar GitHub Copilot Chat (Ctrl+Alt+I)');
            console.log('   3. Verificar se @vscode, @terminal, @workspace, @research estão disponíveis');
        } else if (configurationHealth >= 70) {
            console.log('✅ BOM! Algumas configurações precisam de atenção.');
        } else {
            console.log('⚠️  ATENÇÃO! Várias configurações precisam ser corrigidas.');
        }
        
        return configurationHealth;
    }

    // Métodos auxiliares
    getNestedProperty(obj, path) {
        return path.split('.').reduce((o, p) => o && o[p], obj);
    }

    setNestedProperty(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((o, k) => o[k] = o[k] || {}, obj);
        target[lastKey] = value;
    }

    async checkFileExists(filePath) {
        try {
            await fs.access(path.join(this.basePath, filePath));
            return true;
        } catch {
            return false;
        }
    }

    async run() {
        console.log('🚀 RESOLUÇÃO L1 CONFIGURATION - VIBECODE V6.0\n');
        console.log('='.repeat(80));
        
        const configValid = await this.validateAndFixConfigurations();
        await this.testGitHubCopilotIntegration();
        const health = this.generateConfigurationReport();
        
        console.log('\n='.repeat(80));
        console.log('🏁 L1 CONFIGURATION RESOLUTION CONCLUÍDA');
        
        return health >= 90;
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    const validator = new ConfigurationValidator();
    validator.run().catch(console.error);
}

module.exports = ConfigurationValidator;