---
alwaysApply: true
description: 'Research Implementation + 3-MCP Chain + Context7 Integration + BMAD Method + Implementation Patterns + API Development'
version: '6.0'
title: 'Research Implementation - Advanced Research Protocols, Implementation Patterns & API Development Authority'
type: 'research-implementation-api-authority'
mcp_servers: ['context7-mcp', 'tavily-mcp', 'exa-mcp', 'sequential-thinking', 'desktop-commander']
quality_threshold: 9.6
specialization: 'research-implementation-api-authority'
trigger_keywords: ['research', 'investigate', 'analyze', 'study', 'bmad', 'context7', 'documentation', 'implementation', 'patterns', 'api', 'development', 'architecture']
enforcement_level: 'absolute'
approach: 'research-first-implementation-api-excellence'
globs: '**/*'
priority: 3
---

# 🔬 RESEARCH IMPLEMENTATION + IMPLEMENTATION PATTERNS + API DEVELOPMENT AUTHORITY

## 🎯 MANDATORY RESEARCH PROTOCOL (APEX ENHANCED)

### **Context7-First Research Strategy**

```yaml
CONTEXT7_AUTOMATION:
  primary_triggers:
    - "ALWAYS invoke Context7 FIRST for any code-related query"
    - "Auto-detect library dependencies from workspace files"
    - "Proactive documentation lookup for unfamiliar APIs"
    - "Version-specific documentation retrieval for accurate implementation"
    
  smart_library_detection:
    auto_resolve: "Automatically resolve library names to Context7-compatible IDs"
    known_libraries:
      - "Next.js: /vercel/next.js or /vercel/next.js/v14.3.0-canary.87"
      - "MongoDB: /mongodb/docs"
      - "Supabase: /supabase/supabase"
      - "React: /facebook/react"
      - "TypeScript: /microsoft/typescript"
    topic_focusing: "Auto-focus on specific topics: 'routing', 'hooks', 'authentication'"
    
  documentation_excellence:
    real_time_accuracy: "Access to latest official documentation and API references"
    version_specific: "Precise version-specific implementation patterns"
    best_practices: "Official best practices and recommended patterns"
    troubleshooting: "Official troubleshooting guides and solutions"

CONTEXT7_INTEGRATION_WORKFLOW:
  step_1_library_identification:
    action: "Identify all libraries and frameworks involved in the task"
    automation: "Auto-detect from import statements, package.json, and workspace files"
    
  step_2_context7_research:
    action: "Retrieve latest official documentation using Context7"
    focus_areas: "Specific to the task requirements (routing, hooks, etc.)"
    
  step_3_validation_against_docs:
    action: "Validate all implementation decisions against official documentation"
    quality_gate: "≥95% compliance with official patterns and best practices"
    
  step_4_implementation_backing:
    action: "Ensure every line of code is backed by official documentation"
    documentation_links: "Provide specific documentation references for complex implementations"
```

### **3-MCP Research Chain (MANDATORY)**

```yaml
THREE_MCP_RESEARCH_SEQUENCE:
  mcp_1_context7:
    purpose: "Official documentation and real-time API references"
    mandatory_first: "ALWAYS first in research chain - NO EXCEPTIONS"
    focus: "Latest official documentation, best practices, API specifications"
    output: "Authoritative technical foundation for implementation"
    
  mcp_2_tavily:
    purpose: "Current best practices and industry standards"
    timing: "After Context7 establishes technical foundation"
    focus: "Community best practices, recent developments, implementation patterns"
    output: "Current industry standards and proven patterns"
    
  mcp_3_exa:
    purpose: "Expert-level implementations and advanced patterns"
    timing: "Final research phase for advanced insights"
    focus: "Expert articles, advanced implementations, optimization techniques"
    output: "Expert-level insights and advanced optimization patterns"
    
  synthesis_phase:
    cross_validation: "≥95% consistency across all three research sources"
    pattern_integration: "Merge official docs + best practices + expert insights"
    implementation_strategy: "Research-backed implementation plan with confidence ≥95%"
    quality_assurance: "Research validates ≥9.6/10 quality potential"

RESEARCH_ENFORCEMENT_RULES:
  absolute_requirements:
    - "❌ NO IMPLEMENTATION without completing full 3-MCP research sequence"
    - "❌ NO TASK COMPLETION without achieving ≥9.6/10 quality + research backing"
    - "❌ NO STOPPING until user's query completely resolved + research validated"
    - "✅ ALWAYS continue until perfect solution achieved + research documented"
    
  context7_mandatory_triggers:
    - "Any mention of libraries, frameworks, or APIs"
    - "Implementation tasks involving external dependencies"
    - "Questions about best practices or official patterns"
    - "Debugging or troubleshooting technical issues"
    - "Performance optimization or architecture decisions"
```

## 🏗️ IMPLEMENTATION PATTERNS AUTHORITY

### **Professional Implementation Workflow Patterns**

```yaml
IMPLEMENTATION_PATTERN_AUTHORITY:
  core_implementation_principles:
    research_driven_development: "Every implementation decision backed by comprehensive research"
    pattern_based_architecture: "Use proven, research-validated architectural patterns"
    incremental_development: "Build incrementally with continuous validation"
    test_driven_quality: "Test-first approach with comprehensive coverage"
    documentation_first: "Document architecture and decisions before implementation"
    
  implementation_workflow_patterns:
    discovery_phase:
      requirements_analysis: "Comprehensive requirements gathering with stakeholder validation"
      technical_research: "Deep technical research using 3-MCP chain"
      architecture_design: "System architecture design with pattern validation"
      risk_assessment: "Comprehensive risk analysis and mitigation planning"
      
    design_phase:
      pattern_selection: "Choose optimal patterns based on research findings"
      component_design: "Design components with clear interfaces and responsibilities"
      data_modeling: "Design data models with performance and scalability considerations"
      integration_planning: "Plan integrations with external systems and APIs"
      
    implementation_phase:
      iterative_development: "Develop in small, testable iterations"
      continuous_testing: "Test each component as it's developed"
      code_review: "Mandatory peer review for all implementation"
      documentation_updates: "Keep documentation current with implementation"
      
    validation_phase:
      integration_testing: "Comprehensive integration testing across all components"
      performance_testing: "Validate performance against established benchmarks"
      security_testing: "Security validation and penetration testing"
      user_acceptance: "User acceptance testing with real-world scenarios"

ADVANCED_IMPLEMENTATION_PATTERNS:
  architectural_patterns:
    microservices_implementation:
      service_discovery: "Implement service discovery with health checks and load balancing"
      data_consistency: "Manage data consistency across distributed services"
      communication_patterns: "Use appropriate communication patterns (sync/async)"
      monitoring_observability: "Comprehensive monitoring and observability across services"
      
    event_driven_architecture:
      event_sourcing: "Implement event sourcing for audit trails and state reconstruction"
      cqrs_pattern: "Command Query Responsibility Segregation for scalability"
      event_streaming: "Real-time event streaming with Apache Kafka or similar"
      saga_patterns: "Distributed transaction management with saga patterns"
      
    domain_driven_design:
      bounded_contexts: "Clear bounded contexts with well-defined interfaces"
      aggregate_patterns: "Aggregate root patterns for data consistency"
      repository_patterns: "Repository patterns for data access abstraction"
      domain_services: "Domain services for complex business logic"
      
  integration_patterns:
    api_integration_patterns:
      circuit_breaker: "Circuit breaker pattern for external API resilience"
      retry_patterns: "Exponential backoff and retry strategies"
      timeout_management: "Appropriate timeout configurations for different operations"
      error_handling: "Comprehensive error handling and user-friendly messages"
      
    database_integration_patterns:
      connection_pooling: "Efficient database connection pool management"
      query_optimization: "Query optimization with proper indexing strategies"
      caching_strategies: "Multi-level caching for database query optimization"
      migration_patterns: "Safe database migration patterns with rollback capabilities"
      
    messaging_patterns:
      publish_subscribe: "Pub/Sub patterns for decoupled communication"
      message_queuing: "Message queue patterns for reliable processing"
      dead_letter_queues: "Dead letter queue patterns for error handling"
      message_ordering: "Message ordering strategies for sequential processing"
```

### **Code Quality Implementation Patterns**

```yaml
CODE_QUALITY_IMPLEMENTATION_AUTHORITY:
  design_pattern_implementation:
    creational_patterns:
      factory_pattern: |
        ```typescript
        // Factory Pattern for Service Creation
        interface ServiceFactory {
          createUserService(): UserService;
          createAuthService(): AuthService;
          createEmailService(): EmailService;
        }
        
        class ProductionServiceFactory implements ServiceFactory {
          createUserService(): UserService {
            return new DatabaseUserService(this.createDatabaseConnection());
          }
          
          createAuthService(): AuthService {
            return new JWTAuthService(process.env.JWT_SECRET!);
          }
          
          createEmailService(): EmailService {
            return new SendGridEmailService(process.env.SENDGRID_API_KEY!);
          }
        }
        ```
        
      singleton_pattern: |
        ```typescript
        // Singleton Pattern for Configuration Management
        class ConfigurationManager {
          private static instance: ConfigurationManager;
          private config: AppConfig;
          
          private constructor() {
            this.config = this.loadConfiguration();
          }
          
          static getInstance(): ConfigurationManager {
            if (!ConfigurationManager.instance) {
              ConfigurationManager.instance = new ConfigurationManager();
            }
            return ConfigurationManager.instance;
          }
          
          getConfig(): AppConfig {
            return { ...this.config }; // Return immutable copy
          }
        }
        ```
        
    behavioral_patterns:
      observer_pattern: |
        ```typescript
        // Observer Pattern for Event Management
        interface EventObserver<T> {
          update(data: T): void;
        }
        
        class EventManager<T> {
          private observers: EventObserver<T>[] = [];
          
          subscribe(observer: EventObserver<T>): void {
            this.observers.push(observer);
          }
          
          unsubscribe(observer: EventObserver<T>): void {
            this.observers = this.observers.filter(obs => obs !== observer);
          }
          
          notify(data: T): void {
            this.observers.forEach(observer => observer.update(data));
          }
        }
        ```
        
      strategy_pattern: |
        ```typescript
        // Strategy Pattern for Payment Processing
        interface PaymentStrategy {
          processPayment(amount: number, details: PaymentDetails): Promise<PaymentResult>;
        }
        
        class CreditCardStrategy implements PaymentStrategy {
          async processPayment(amount: number, details: CreditCardDetails): Promise<PaymentResult> {
            // Credit card processing logic
            return await this.processCreditCard(amount, details);
          }
        }
        
        class PaymentProcessor {
          constructor(private strategy: PaymentStrategy) {}
          
          setStrategy(strategy: PaymentStrategy): void {
            this.strategy = strategy;
          }
          
          async processPayment(amount: number, details: PaymentDetails): Promise<PaymentResult> {
            return await this.strategy.processPayment(amount, details);
          }
        }
        ```
        
  error_handling_patterns:
    comprehensive_error_handling: |
      ```typescript
      // Comprehensive Error Handling Pattern
      interface ErrorContext {
        operation: string;
        userId?: string;
        metadata?: Record<string, any>;
      }
      
      class ApplicationError extends Error {
        constructor(
          message: string,
          public code: string,
          public statusCode: number,
          public context?: ErrorContext
        ) {
          super(message);
          this.name = this.constructor.name;
        }
      }
      
      class ErrorHandler {
        static handle(error: Error, context: ErrorContext): never {
          // Log error with context
          logger.error('Application error occurred', {
            error: error.message,
            stack: error.stack,
            context
          });
          
          // Determine appropriate user-facing message
          const userMessage = error instanceof ApplicationError 
            ? error.message 
            : 'An unexpected error occurred';
            
          // Throw user-friendly error
          throw new ApplicationError(
            userMessage,
            error instanceof ApplicationError ? error.code : 'INTERNAL_ERROR',
            error instanceof ApplicationError ? error.statusCode : 500,
            context
          );
        }
      }
      ```
      
    result_pattern: |
      ```typescript
      // Result Pattern for Error Handling
      type Result<T, E = Error> = 
        | { success: true; data: T }
        | { success: false; error: E };
      
      class ResultHandler {
        static success<T>(data: T): Result<T> {
          return { success: true, data };
        }
        
        static failure<E>(error: E): Result<never, E> {
          return { success: false, error };
        }
        
        static async from<T>(promise: Promise<T>): Promise<Result<T>> {
          try {
            const data = await promise;
            return ResultHandler.success(data);
          } catch (error) {
            return ResultHandler.failure(error as Error);
          }
        }
      }
      ```
```

## 🌐 API DEVELOPMENT AUTHORITY

### **RESTful API Implementation Patterns**

```yaml
API_DEVELOPMENT_EXCELLENCE:
  restful_api_design_principles:
    resource_modeling: "Model resources as nouns with clear hierarchical relationships"
    http_verb_usage: "Use appropriate HTTP verbs (GET, POST, PUT, DELETE, PATCH)"
    status_code_consistency: "Consistent HTTP status codes across all endpoints"
    url_design: "Clean, predictable URL structures following REST conventions"
    
  advanced_api_patterns:
    api_versioning_strategies:
      url_versioning: "/api/v1/users - Clear version in URL path"
      header_versioning: "Accept: application/vnd.api+json;version=1 - Version in headers"
      query_versioning: "/api/users?version=1 - Version as query parameter"
      content_negotiation: "Use Accept headers for different response formats"
      
    authentication_authorization:
      jwt_implementation: |
        ```typescript
        // JWT Authentication Implementation
        interface JWTPayload {
          userId: string;
          email: string;
          roles: string[];
          iat: number;
          exp: number;
        }
        
        class JWTAuthService {
          constructor(private secretKey: string) {}
          
          generateToken(user: User): string {
            const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
              userId: user.id,
              email: user.email,
              roles: user.roles
            };
            
            return jwt.sign(payload, this.secretKey, {
              expiresIn: '24h',
              issuer: 'api-server',
              audience: 'client-app'
            });
          }
          
          verifyToken(token: string): JWTPayload | null {
            try {
              return jwt.verify(token, this.secretKey) as JWTPayload;
            } catch (error) {
              return null;
            }
          }
        }
        ```
        
      role_based_access: |
        ```typescript
        // Role-Based Access Control Implementation
        interface Permission {
          resource: string;
          action: string;
          conditions?: Record<string, any>;
        }
        
        class AccessControlService {
          private rolePermissions: Map<string, Permission[]> = new Map();
          
          addRolePermissions(role: string, permissions: Permission[]): void {
            this.rolePermissions.set(role, permissions);
          }
          
          hasPermission(userRoles: string[], resource: string, action: string): boolean {
            for (const role of userRoles) {
              const permissions = this.rolePermissions.get(role) || [];
              const hasPermission = permissions.some(
                p => p.resource === resource && p.action === action
              );
              if (hasPermission) return true;
            }
            return false;
          }
          
          checkPermission(userRoles: string[], resource: string, action: string): void {
            if (!this.hasPermission(userRoles, resource, action)) {
              throw new ApplicationError(
                'Insufficient permissions',
                'FORBIDDEN',
                403
              );
            }
          }
        }
        ```
        
    advanced_api_features:
      pagination_implementation: |
        ```typescript
        // Advanced Pagination Implementation
        interface PaginationParams {
          page?: number;
          limit?: number;
          cursor?: string;
          sort?: string;
          order?: 'asc' | 'desc';
        }
        
        interface PaginatedResponse<T> {
          data: T[];
          pagination: {
            currentPage: number;
            totalPages: number;
            totalItems: number;
            hasNext: boolean;
            hasPrevious: boolean;
            nextCursor?: string;
            previousCursor?: string;
          };
        }
        
        class PaginationService {
          static async paginate<T>(
            query: QueryBuilder,
            params: PaginationParams
          ): Promise<PaginatedResponse<T>> {
            const page = params.page || 1;
            const limit = Math.min(params.limit || 10, 100); // Max 100 items
            const offset = (page - 1) * limit;
            
            // Get total count for metadata
            const totalItems = await query.clone().count('* as count').first();
            const totalPages = Math.ceil(totalItems.count / limit);
            
            // Apply pagination to query
            const data = await query
              .offset(offset)
              .limit(limit)
              .orderBy(params.sort || 'id', params.order || 'asc');
            
            return {
              data,
              pagination: {
                currentPage: page,
                totalPages,
                totalItems: totalItems.count,
                hasNext: page < totalPages,
                hasPrevious: page > 1
              }
            };
          }
        }
        ```
        
      rate_limiting: |
        ```typescript
        // Rate Limiting Implementation
        interface RateLimitConfig {
          windowMs: number;
          maxRequests: number;
          skipSuccessfulRequests?: boolean;
          skipFailedRequests?: boolean;
        }
        
        class RateLimitService {
          private requestCounts: Map<string, { count: number; resetTime: number }> = new Map();
          
          constructor(private config: RateLimitConfig) {}
          
          isAllowed(clientId: string): boolean {
            const now = Date.now();
            const clientData = this.requestCounts.get(clientId);
            
            // Reset window if expired
            if (!clientData || now > clientData.resetTime) {
              this.requestCounts.set(clientId, {
                count: 1,
                resetTime: now + this.config.windowMs
              });
              return true;
            }
            
            // Check if within limit
            if (clientData.count < this.config.maxRequests) {
              clientData.count++;
              return true;
            }
            
            return false;
          }
          
          getRemainingRequests(clientId: string): number {
            const clientData = this.requestCounts.get(clientId);
            if (!clientData) return this.config.maxRequests;
            
            return Math.max(0, this.config.maxRequests - clientData.count);
          }
        }
        ```

API_SECURITY_PATTERNS:
  security_best_practices:
    input_validation: |
      ```typescript
      // Comprehensive Input Validation
      import { z } from 'zod';
      
      const CreateUserSchema = z.object({
        email: z.string().email().max(255),
        password: z.string().min(8).max(128).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
        name: z.string().min(1).max(100),
        role: z.enum(['user', 'admin', 'moderator']).optional()
      });
      
      class ValidationService {
        static validate<T>(schema: z.ZodSchema<T>, data: unknown): T {
          try {
            return schema.parse(data);
          } catch (error) {
            if (error instanceof z.ZodError) {
              throw new ApplicationError(
                'Validation failed',
                'VALIDATION_ERROR',
                400,
                { validationErrors: error.errors }
              );
            }
            throw error;
          }
        }
      }
      ```
      
    sql_injection_prevention: |
      ```typescript
      // SQL Injection Prevention
      class SecureQueryBuilder {
        private query: string = '';
        private params: any[] = [];
        
        select(columns: string[]): this {
          // Whitelist column names to prevent injection
          const safeColumns = columns.filter(col => /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(col));
          this.query += `SELECT ${safeColumns.join(', ')} `;
          return this;
        }
        
        where(condition: string, value: any): this {
          this.query += `WHERE ${condition} `;
          this.params.push(value);
          return this;
        }
        
        execute(): Promise<any[]> {
          return database.raw(this.query, this.params);
        }
      }
      ```
      
    cors_configuration: |
      ```typescript
      // CORS Configuration
      const corsOptions = {
        origin: (origin: string | undefined, callback: Function) => {
          const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];
          
          // Allow requests with no origin (mobile apps, etc.)
          if (!origin) return callback(null, true);
          
          if (allowedOrigins.includes(origin)) {
            callback(null, true);
          } else {
            callback(new Error('Not allowed by CORS'));
          }
        },
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        allowedHeaders: ['Content-Type', 'Authorization'],
        maxAge: 86400 // 24 hours
      };
      ```
```

## 🧙 BMAD METHOD INTEGRATION

### **Research-Driven BMAD Workflow**

```yaml
BMAD_RESEARCH_INTEGRATION:
  bmad_research_triggers:
    - "Story creation and epic planning"
    - "Architecture and system design decisions"
    - "Implementation pattern selection"
    - "Quality assurance and validation protocols"
    
  research_enhanced_bmad:
    story_creation: "Context7 research for technology constraints and capabilities"
    epic_planning: "Tavily research for industry best practices and patterns"
    architecture_design: "Exa research for expert architectural insights"
    implementation: "Full 3-MCP research for optimal implementation patterns"
    
  bmad_quality_elevation:
    research_backed_stories: "All stories backed by research-validated feasibility"
    evidence_based_decisions: "All BMAD decisions supported by research evidence"
    continuous_validation: "Ongoing research validation throughout BMAD workflow"
    pattern_optimization: "Apply research insights to optimize BMAD patterns"

BMAD_RESEARCH_WORKFLOW:
  phase_1_research_planning:
    action: "Research technical feasibility before story creation"
    tools: "Context7 for official capabilities + Tavily for implementation patterns"
    
  phase_2_research_validation:
    action: "Validate story technical requirements against research findings"
    tools: "Cross-reference all three research sources for consistency"
    
  phase_3_research_implementation:
    action: "Implement stories using research-backed patterns and practices"
    tools: "Apply research insights during hands-on implementation"
    
  phase_4_research_optimization:
    action: "Optimize implementation based on expert research insights"
    tools: "Exa insights for advanced optimization techniques"
```

### **Advanced Research Automation**

```yaml
RESEARCH_AUTOMATION_PROTOCOLS:
  auto_research_triggers:
    uncertainty_detection: "Auto-trigger research when uncertainty detected"
    complexity_threshold: "Research required for complexity ≥5.0"
    technology_changes: "Research when new technologies or versions detected"
    optimization_opportunities: "Research for performance improvement opportunities"
    
  intelligent_research_scoping:
    focused_research: "Narrow research scope to specific technical areas"
    comprehensive_research: "Broad research for architectural and strategic decisions"
    comparative_research: "Multi-option research for technology selection"
    validation_research: "Verification research for critical implementations"
    
  research_quality_assurance:
    source_verification: "Validate research sources for credibility and currency"
    cross_validation: "Ensure consistency across multiple research sources"
    bias_detection: "Identify and compensate for research bias"
    completeness_check: "Ensure research comprehensively addresses requirements"

CONTINUOUS_RESEARCH_IMPROVEMENT:
  research_pattern_learning:
    successful_patterns: "Learn from successful research → implementation patterns"
    failure_analysis: "Analyze research gaps that led to implementation issues"
    efficiency_optimization: "Optimize research processes for speed and accuracy"
    quality_enhancement: "Continuously improve research quality and depth"
    
  adaptive_research_strategies:
    context_aware_research: "Adapt research strategy based on project context"
    technology_specific_research: "Apply technology-specific research approaches"
    complexity_appropriate_research: "Scale research depth to task complexity"
    time_optimized_research: "Balance research depth with time constraints"
```

## 🔬 RESEARCH VALIDATION & SYNTHESIS

### **Research Quality Assurance**

```yaml
RESEARCH_QUALITY_FRAMEWORK:
  source_quality_assessment:
    official_documentation: "Weight: 40% - Authoritative and current"
    community_best_practices: "Weight: 30% - Proven and widely adopted"
    expert_insights: "Weight: 20% - Advanced and optimization-focused"
    cross_validation: "Weight: 10% - Consistency and reliability check"
    
  research_completeness_criteria:
    technical_accuracy: "≥95% accuracy in technical details and specifications"
    implementation_feasibility: "≥95% confidence in implementation viability"
    best_practice_alignment: "≥90% alignment with industry best practices"
    performance_optimization: "≥85% coverage of performance considerations"
    
  synthesis_quality_standards:
    information_integration: "Seamless integration of all research sources"
    contradiction_resolution: "Clear resolution of any conflicting information"
    implementation_clarity: "Clear, actionable implementation guidance"
    risk_assessment: "Comprehensive risk assessment and mitigation strategies"

RESEARCH_VALIDATION_PROTOCOL:
  consistency_verification:
    cross_source_validation: "Verify consistency across Context7, Tavily, and Exa"
    technical_verification: "Validate technical claims against official documentation"
    pattern_verification: "Confirm patterns work across different contexts"
    performance_verification: "Validate performance claims and optimizations"
    
  implementation_validation:
    feasibility_check: "Confirm implementation feasibility in current environment"
    dependency_validation: "Verify all dependencies are available and compatible"
    integration_validation: "Ensure smooth integration with existing systems"
    testing_validation: "Confirm testability and validation approaches"
```

### **Research-Driven Implementation Excellence**

```yaml
IMPLEMENTATION_EXCELLENCE_THROUGH_RESEARCH:
  research_backed_development:
    pattern_application: "Apply research-validated patterns and practices"
    best_practice_integration: "Integrate industry best practices throughout implementation"
    optimization_implementation: "Apply expert optimization techniques from research"
    quality_assurance: "Use research-backed quality assurance techniques"
    
  continuous_research_integration:
    real_time_validation: "Validate implementation decisions against research in real-time"
    adaptive_improvement: "Continuously improve implementation based on ongoing research"
    pattern_refinement: "Refine implementation patterns based on research insights"
    quality_elevation: "Elevate quality standards based on research findings"
    
  research_documentation:
    decision_rationale: "Document research-based rationale for all major decisions"
    pattern_documentation: "Document successful research → implementation patterns"
    optimization_documentation: "Document research-backed optimization techniques"
    learning_documentation: "Document learnings for future research and implementation"
```

## 🚫 RESEARCH ANTI-PATTERNS

```yaml
RESEARCH_VIOLATIONS:
  research_shortcuts:
    - "❌ NEVER skip Context7 research for official documentation"
    - "❌ NEVER bypass 3-MCP research sequence for complex tasks"
    - "❌ NEVER implement without research validation"
    - "❌ NEVER ignore research findings that contradict assumptions"
    
  quality_compromises:
    - "❌ NEVER accept research with <95% confidence"
    - "❌ NEVER proceed with unresolved research contradictions"
    - "❌ NEVER implement patterns not backed by research"
    - "❌ NEVER skip research synthesis and validation"
    
  implementation_violations:
    - "❌ NEVER implement without research-backed patterns"
    - "❌ NEVER ignore research-identified risks or limitations"
    - "❌ NEVER skip research-recommended optimizations"
    - "❌ NEVER fail to document research-based decisions"
```

## 🔒 RESEARCH IMPLEMENTATION COMPLIANCE

```yaml
RESEARCH_IMPLEMENTATION_OATH:
  - "I will ALWAYS begin with Context7 research for official documentation"
  - "I will ALWAYS complete the full 3-MCP research sequence for comprehensive understanding"
  - "I will ALWAYS validate implementation decisions against research findings"
  - "I will ALWAYS integrate research insights throughout the implementation process"
  - "I will ALWAYS document research-based decisions and rationale"
  - "I will ALWAYS maintain ≥9.6/10 quality through research-backed excellence"
  - "I will NEVER implement without comprehensive research backing"

RESEARCH_EXCELLENCE_COMMITMENT:
  - "EVERY implementation backed by comprehensive research"
  - "EVERY decision validated against authoritative sources"
  - "EVERY pattern proven through research and best practices"
  - "EVERY optimization supported by expert insights"
  - "EVERY quality standard elevated through research findings"
```

---

**🔬 RESEARCH IS THE FOUNDATION OF IMPLEMENTATION EXCELLENCE**
**CONTEXT7 + TAVILY + EXA = COMPREHENSIVE UNDERSTANDING**
**RESEARCH FIRST - IMPLEMENT SECOND - EXCEL ALWAYS**
**IMPLEMENTATION PATTERNS + API EXCELLENCE = PROFESSIONAL DEVELOPMENT**