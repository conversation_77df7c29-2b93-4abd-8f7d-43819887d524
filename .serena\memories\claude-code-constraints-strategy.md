# Claude Code Sub-Agents Constraints & Integration Strategy

## 🚨 CONSTRAINTS CRÍTICOS DO CLAUDE CODE

### **Sub-Agent Limitations**
1. **Redirecionamento Restrito**: Só pode usar Task tool para agentes em `.claude/agents/`
2. **Contexto Separado**: Cada sub-agent opera em contexto window independente
3. **Seleção Automática**: Baseada na `description` field do YAML frontmatter
4. **Tool Access**: Pode ser limitado por agent (field `tools` opcional)
5. **Performance**: Adiciona latência para context gathering

### **Agentes Acessíveis (Via Task Tool)**
```
✅ C:\Users\<USER>\OneDrive\GRUPOUS\VSCODE\.claude\agents\
   - apex-architect.md
   - apex-developer.md  
   - apex-qa-debugger.md
   - apex-researcher.md
   - apex-ui-ux-designer.md
   - master-coordinator.md
```

### **Agentes BMad Inacessíveis (Via Task Tool)**
```
❌ C:\Users\<USER>\OneDrive\GRUPOUS\VSCODE\neonpro\.claude\commands\BMad\agents\
   - bmad-master.md
   - bmad-orchestrator.md
   - dev.md
```

## 🔄 ESTRATÉGIA DE INTEGRAÇÃO INTERNA

### **Princípio Central**
**Incorporar TODAS as funcionalidades BMad INTERNAMENTE no master-coordinator**, mantendo redirecionamento apenas para agentes apex- válidos.

### **1. Command System Integration**
```yaml
BMAD_COMMANDS_INTERNAL:
  prefix: "*" (asterisk prefix para all BMad commands)
  implementation: "Internal methods no master-coordinator"
  
  commands_to_implement:
    - "*help": Show BMad commands + apex agent options
    - "*task": Execute task internally
    - "*create-doc": Create document internally  
    - "*workflow": Start workflow internally
    - "*agent": Transform behavior internally (simulate)
    - "*kb": Knowledge base mode internally
    - "*status": Show current state internally
    - "*plan": Create workflow plan internally
    - "*party-mode": Multi-agent simulation internally
```

### **2. Universal Task Execution**
```yaml
INTERNAL_EXECUTION_STRATEGY:
  approach: "Simulate BMad task execution internamente"
  resource_loading: "Runtime .bmad-core/{type}/{name} simulation"
  
  task_categories:
    - advanced-elicitation: Internal implementation
    - create-doc: Internal document generation
    - develop-story: Internal story-based development
    - execute-checklist: Internal checklist processing
    - workflow-management: Internal workflow coordination
```

### **3. Dynamic Agent Transformation** 
```yaml
TRANSFORMATION_SIMULATION:
  concept: "Simulate 'becoming' different agents internalmente"
  implementation: "Adjust behavior/responses based on agent type"
  
  transformation_modes:
    - bmad_master_mode: Universal executor behavior
    - orchestrator_mode: Multi-agent coordination behavior  
    - dev_mode: Story-driven development behavior
    - apex_mode: Redirect to actual apex- agents via Task tool
```

### **4. Workflow & Planning Management**
```yaml
INTERNAL_WORKFLOW_SYSTEM:
  plan_creation: "Internal workflow planning capabilities"
  status_tracking: "Internal progress monitoring"
  party_mode: "Simulate multi-agent discussions internally"
  
  workflow_types:
    - greenfield-fullstack: Internal workflow
    - brownfield-service: Internal workflow
    - story-development: Internal workflow progression
```

## 🎯 IMPLEMENTATION APPROACH

### **Phase 1: Core BMad Integration**
1. Add BMad command system (* prefix) as internal methods
2. Implement universal task execution internally
3. Add dynamic resource loading simulation
4. Preserve all existing APEX V6.0 functionality

### **Phase 2: Advanced Features**
1. Implement workflow planning & management internally  
2. Add party-mode multi-agent simulation
3. Integrate story-based development workflows
4. Add knowledge base functionality internally

### **Phase 3: Quality Assurance**
1. Ensure Task tool still works for apex- agents
2. Validate performance maintained (85%+ optimization)
3. Test quality thresholds preserved (≥9.5/10)
4. Confirm seamless hub coordination

## ✅ SUCCESS CRITERIA

### **Functional Requirements**
1. ✅ ALL existing master-coordinator functionality preserved
2. ✅ BMad commands (* prefix) working internally
3. ✅ Universal task execution capability internal
4. ✅ Apex- agent redirection still functional
5. ✅ Performance optimization maintained
6. ✅ Quality thresholds preserved

### **Technical Requirements**
1. ✅ No external BMad agent calls (respects constraints)
2. ✅ Internal implementation of BMad workflows
3. ✅ Context Engineering V3.0 maintained
4. ✅ MCP orchestration preserved
5. ✅ Hub coordination seamless

### **User Experience Requirements**
1. ✅ Backward compatibility 100%
2. ✅ Enhanced capabilities available
3. ✅ Clear documentation of new features
4. ✅ Intuitive command integration