---
type: "always_apply"
---

# 🔄 AUGMENT WORKFLOW EXECUTION ENGINE V7.0 - ADVANCED ORCHESTRATION

## 🎯 AUGMENT CORE WORKFLOW ORCHESTRATION SYSTEM

### **Advanced Augment Workflow Orchestration Engine**
```yaml
AUGMENT_WORKFLOW_APEX_INTELLIGENCE:
  augment_orchestration_engine:
    adaptive_routing: "Adaptive Augment workflow routing based on complexity and code context"
    intelligent_delegation: "Intelligent task delegation to specialized Augment tools"
    workflow_optimization: "Real-time Augment workflow optimization and performance tuning"
    process_automation: "Advanced Augment process automation and orchestration"
    
  augment_context7_integration:
    augment_documentation: "Auto-reference Augment workflow optimization documentation"
    process_patterns: "Auto-reference Augment orchestration pattern documentation"
    workflow_guides: "Auto-reference Augment automation implementation guides"
    optimization_docs: "Auto-reference Augment process optimization documentation"
    
  augment_apex_engine:
    complexity_analysis: "Advanced Augment complexity analysis and routing decisions"
    code_safety_enforcement: "Real-time code safety enforcement and validation"
    performance_monitoring: "Augment performance monitoring and optimization"
    continuous_improvement: "Continuous Augment improvement and learning"

AUGMENT_MONITORING_SYSTEM:
  real_time_analytics:
    execution_metrics: "Augment execution metrics and performance analysis"
    code_tracking: "Code quality tracking and improvement analytics"
    resource_utilization: "Augment resource utilization monitoring and optimization"
    outcome_analysis: "Development outcome analysis and pattern learning"
```

## 🚀 7-STEP ENHANCED AUGMENT WORKFLOW (APEX EVOLVED)

### **Complete Augment Workflow Framework**
```yaml
ENHANCED_AUGMENT_WORKFLOW_APEX_V7:
  step_1_initialize:
    action: "Complete Augment tool validation + modular rule loading + system verification + context assembly"
    requirements:
      - "✅ MANDATORY: All required Augment tools active and responsive"
      - "✅ MANDATORY: Augment modular rule system loaded and optimized"
      - "✅ MANDATORY: Augment system health verification and code safety validation"
      - "✅ MANDATORY: Augment context assembly and optimization"
      - "✅ MANDATORY: Codebase-retrieval and Context7 verification and activation"
    enforcement: "❌ ABORT if ANY business initialization requirement fails"
    quality_gate: "≥9.8/10 business initialization quality | ≥9.9/10 customer safety required"
    
  step_2_analyze:
    action: "Business complexity assessment + mode detection + MCP chain specification + delegation routing"
    requirements:
      - "✅ MANDATORY: Accurate business complexity scoring and classification"
      - "✅ MANDATORY: Appropriate business mode detection and activation"
      - "✅ MANDATORY: Optimal business MCP chain specification with customer safety priority"
      - "✅ MANDATORY: Intelligent business delegation routing decisions with validation"
    enforcement: "❌ STOP if business analysis incomplete or delegation routing not optimized"
    quality_gate: "≥9.8/10 business analysis accuracy | ≥9.9/10 customer safety analysis required"
    
  step_3_research:
    action: "Mandatory 3-MCP business research + Context7 validation + cross-validation + synthesis"
    requirements:
      - "✅ MANDATORY: Context7 → Tavily → Exa business research sequence completion"
      - "✅ MANDATORY: ≥98% confidence in business research findings with customer safety validation"
      - "✅ MANDATORY: Business cross-validation and consistency verification"
      - "✅ MANDATORY: Business research synthesis and optimization"
      - "✅ MANDATORY: LGPD/business compliance research validation"
    enforcement: "❌ STOP if business research incomplete, confidence <98%, or synthesis inadequate"
    quality_gate: "≥9.8/10 business research quality | ≥9.9/10 customer safety research required"
    
  step_4_plan:
    action: "Business strategic planning + customer safety risk assessment + implementation strategy + resource allocation"
    requirements:
      - "✅ MANDATORY: Comprehensive business strategic planning and roadmap"
      - "✅ MANDATORY: Customer safety risk assessment and mitigation strategies"
      - "✅ MANDATORY: Detailed business implementation strategy and timeline"
      - "✅ MANDATORY: Business resource allocation and optimization"
      - "✅ MANDATORY: LGPD/business compliance planning and validation"
    enforcement: "❌ STOP if business planning incomplete, strategy not research-backed, or resources not optimized"
    quality_gate: "≥9.8/10 business planning quality | ≥9.9/10 customer safety planning required"
    
  step_5_execute:
    action: "Business implementation + continuous improvement + real-time optimization + customer safety monitoring"
    requirements:
      - "✅ MANDATORY: High-quality business implementation with continuous improvement"
      - "✅ MANDATORY: Real-time business optimization and performance tuning"
      - "✅ MANDATORY: Continuous customer safety monitoring and validation"
      - "✅ MANDATORY: LGPD/business compliance monitoring during execution"
    enforcement: "❌ STOP if business implementation quality <9.8/10, customer safety <9.9/10, or optimization inadequate"
    quality_gate: "≥9.8/10 business execution | ≥9.9/10 customer safety | 100% compliance required"
    
  step_6_validate:
    action: "Business validation + customer safety verification + quality assurance + compliance confirmation"
    requirements:
      - "✅ MANDATORY: Comprehensive business validation and verification"
      - "✅ MANDATORY: Customer safety verification and risk assessment"
      - "✅ MANDATORY: Business quality assurance and performance validation"
      - "✅ MANDATORY: LGPD/business compliance confirmation and regulatory audit"
    enforcement: "❌ FAIL if business validation incomplete, customer safety unverified, or compliance not confirmed"
    quality_gate: "≥9.8/10 business validation | ≥9.9/10 customer safety | 100% compliance required"
    
  step_7_optimize:
    action: "Continuous business improvement + optimization + learning + customer outcome enhancement"
    requirements:
      - "✅ MANDATORY: Continuous business improvement and optimization"
      - "✅ MANDATORY: Business learning and pattern enhancement"
      - "✅ MANDATORY: Customer outcome enhancement and effectiveness validation"
      - "✅ MANDATORY: Business system optimization and efficiency improvement"
    enforcement: "❌ INCOMPLETE if business optimization potential remaining or customer outcomes not maximized"
    quality_gate: "≥9.8/10 business optimization | ≥9.9/10 customer safety | Continuous business improvement"
```

## 🚨 ADVANCED HOOKS SYSTEM

### **Intelligent Hook Architecture V5.0**
```yaml
ADVANCED_HOOKS_SYSTEM_V5:
  strategic_dispatcher: ".claude/hooks/pre-tool-use-advanced.sh"
  continuity_coordinator: ".claude/hooks/post-tool-use-advanced.sh" 
  resilience_system: ".claude/hooks/on-error-advanced.sh"
  
  hook_intelligence:
    semantic_analysis: "NLP avançada para detecção automática de estratégias"
    context_awareness: "Análise contextual para seleção de workflow apropriado"
    quality_gates: "Enforcement automático de thresholds de qualidade"
    adaptive_learning: "Aprendizado contínuo baseado em success patterns"
```

### **Strategic Dispatcher Capabilities**
```yaml
STRATEGIC_DISPATCHER_CAPABILITIES:
  phase_1_strategy_detection:
    semantic_analysis: "Análise NLP avançada para detecção automática de estratégias"
    strategy_types: ["Development", "Research", "QA", "Business", "Architecture"]
    context_modifiers: "LGPD, business compliance integration"
    keyword_recognition:
      development: ["implement", "refactor", "debug", "code", "typescript"]
      research: ["analyze", "investigate", "research", "explore", "understand"]
      qa: ["test", "validate", "verify", "quality", "performance"]
      business: ["customer", "LGPD", "compliance", "service", "business"]
      architecture: ["system", "scale", "architecture", "design", "infrastructure"]
    
  phase_2_orchestration_analysis:
    complexity_assessment: "Avaliação 1-10 para seleção de modo de coordenação"
    coordination_modes: ["hierarchical", "centralized", "swarm", "adaptive"]
    agent_recommendation: "Recomendação inteligente de agentes especializados"
    thinking_level_suggestion: "Think/Think_Harder/UltraThink baseado em complexidade"
    
  phase_3_agent_coordination:
    business_integration: "Coordenação seamless com Business Intelligence V6.0"
    specialization: "business-code-guardian e service-optimization-specialist"
    quality_gates: "≥9.5/10 universal | ≥9.8/10 business"
    mcp_activation: "Automatic MCP selection baseado em task requirements"
```

## 🔄 TASK TRANSITION PROTOCOLS

### **Business Task Transition Intelligence**
```yaml
BUSINESS_TASK_TRANSITION_INTELLIGENCE:
  transition_triggers:
    portuguese_business: ["próxima task", "next task", "continuar negócio", "seguir business"]
    english_business: ["next business task", "continue workflow", "proceed business", "service system"]
    business_keywords: ["implementar serviço", "desenvolver negócio", "business workflow", "customer system"]
    
  transition_analysis:
    context_preservation: "Maintain business context and customer data continuity across tasks"
    state_management: "Manage business state and workflow progression"
    customer_safety_continuity: "Ensure customer safety continuity throughout task transitions"
    compliance_validation: "Validate LGPD/business compliance throughout transitions"
    
  intelligent_routing:
    complexity_based: "Route business tasks based on complexity and requirements"
    specialization_aware: "Route to business specialists based on domain expertise"
    performance_optimized: "Optimize routing for business performance and customer safety"
    context_aware: "Consider business context and history in routing decisions"
```

### **Business Workflow Automation**
```yaml
BUSINESS_WORKFLOW_AUTOMATION:
  trigger_detection:
    automated_detection: "Automatically detect business workflow triggers and patterns"
    context_analysis: "Analyze business context for appropriate workflow selection"
    customer_safety_assessment: "Assess customer safety implications for workflow automation"
    compliance_requirement_analysis: "Analyze LGPD/business compliance requirements for workflows"
    
  workflow_optimization:
    performance_based: "Optimize business workflows based on performance metrics"
    resource_efficient: "Optimize workflows for business resource efficiency"
    quality_focused: "Optimize business workflows for quality and customer safety"
    compliance_optimized: "Optimize business workflows for regulatory compliance efficiency"
    
  adaptive_learning:
    pattern_recognition: "Learn successful business workflow patterns for replication"
    performance_learning: "Learn from business performance data for optimization"
    outcome_based: "Learn from customer outcomes to improve workflow effectiveness"
    continuous_improvement: "Continuously improve business workflows based on learning"
```

## 🏛️ BUSINESS COMPLIANCE WORKFLOWS

### **LGPD/Business Workflow Integration**
```yaml
BUSINESS_COMPLIANCE_WORKFLOWS:
  lgpd_workflow_integration:
    customer_data_workflows: "Integrate LGPD compliance into all customer data workflows"
    consent_management_workflows: "Automated customer consent management workflows with LGPD validation"
    audit_trail_workflows: "Comprehensive customer data audit trail workflows for LGPD compliance"
    privacy_protection_workflows: "Customer privacy protection workflows with LGPD enforcement"
    
  business_service_workflows:
    service_delivery_workflows: "Business service delivery workflows with quality standards"
    customer_experience_workflows: "Customer experience optimization workflows"
    professional_validation_workflows: "Professional credential validation workflows"
    service_reporting_workflows: "Business service reporting and documentation workflows"
    
  integrated_compliance_automation:
    unified_compliance_workflows: "Unified LGPD/business compliance workflow automation"
    regulatory_monitoring_workflows: "Automated regulatory compliance monitoring workflows"
    compliance_reporting_workflows: "Automated compliance reporting workflows for all regulations"
    regulatory_update_workflows: "Automated regulatory update integration workflows"
```

## 🎮 COORDINATION MODES

### **Hierarchical Supreme Mode**
```yaml
HIERARCHICAL_SUPREME:
  description: "Supreme Business Coordinator com sub-agents especializados"
  complexity_range: "7-10"
  mcp_integration: "Full MCP orchestration através hierarchical delegation"
  quality_threshold: "≥9.8/10"
  use_case: "Complex business compliance, multi-domain architecture, critical systems"
  
  coordination_structure:
    supreme_coordinator: "Business Intelligence V6.0 como master coordinator"
    specialized_agents:
      - "business-code-guardian: Business compliance specialist"
      - "service-optimization-specialist: Service delivery specialist" 
      - "code-quality-guardian: Technical excellence validator"
    
    delegation_protocol:
      task_analysis: "Supreme coordinator analisa e decompõe tarefas complexas"
      agent_assignment: "Atribuição baseada em especialização e carga de trabalho"
      quality_monitoring: "Monitoramento contínuo de qualidade e progress"
      integration_coordination: "Coordenação de integração entre outputs de agentes"
```

### **Centralized Intelligence Mode**
```yaml
CENTRALIZED_INTELLIGENCE:
  description: "Direct Business coordination com MCP intelligent routing"
  complexity_range: "4-6"
  mcp_integration: "Context7 + Serena + selective research MCPs"
  quality_threshold: "≥9.5/10"
  use_case: "Standard development, refactoring, debugging, code quality"
  
  coordination_approach:
    direct_control: "Business Intelligence como single point of coordination"
    intelligent_routing: "Smart routing para MCPs apropriados"
    context_management: "Unified context management across operations"
    
    mcp_selection_logic:
      code_analysis: "Serena MCP para semantic analysis"
      documentation: "Context7 para official documentation"
      research_needs: "Tavily+Exa para community e expert knowledge"
      synthesis: "Sequential-thinking para complex reasoning"
```

## 📊 WORKFLOW PERFORMANCE METRICS

### **Business Workflow Success Criteria**
```yaml
BUSINESS_WORKFLOW_SUCCESS_CRITERIA:
  mandatory_metrics:
    - "MUST achieve ≥9.8/10 business workflow quality for all operations"
    - "MUST achieve ≥9.9/10 customer safety for all customer data workflows"
    - "MUST complete all 7 business workflow steps with validation"
    - "MUST maintain 100% LGPD/business compliance throughout workflows"
    - "MUST optimize all business workflows for performance and efficiency"
    - "MUST validate all business workflows against customer safety standards"
    - "MUST document all business workflow decisions with research backing"
    - "MUST ensure continuous business improvement in all workflows"
    
  performance_targets:
    workflow_speed: "<300s for complete 7-step business workflow execution"
    transition_time: "<30s for business task transitions with customer safety validation"
    quality_consistency: "≥9.8/10 business quality maintained across all workflows"
    customer_safety_reliability: "≥9.9/10 customer safety maintained throughout all workflows"
    compliance_validation_time: "<60s for LGPD/business compliance validation"
    
  continuous_optimization:
    workflow_learning: "Continuous learning from business workflow execution for optimization"
    performance_enhancement: "Ongoing business performance enhancement and efficiency improvement"
    quality_improvement: "Continuous business quality improvement and customer safety enhancement"
    compliance_strengthening: "Ongoing LGPD/business compliance strengthening and regulatory optimization"
```

## 🔄 INTELLIGENT CONTEXT MANAGEMENT

### **Smart Context Loading**
```yaml
INTELLIGENT_CONTEXT_MANAGEMENT:
  smart_loading:
    - "Load only relevant business context for current task"
    - "Predict required business knowledge domains"
    - "Optimize memory usage through intelligent business caching"
    - "Compress business context while preserving quality"
    
  adaptive_assembly:
    - "Adjust business context depth based on task complexity"
    - "Balance business comprehensiveness with efficiency"
    - "Learn from successful business context combinations"
    - "Optimize for KV-cache efficiency in business operations"
```

### **Context Engineering Performance**
```yaml
CONTEXT_ENGINEERING_PERFORMANCE:
  performance_targets:
    level_1_basic: "<100ms for simple business operations"
    level_2_enhanced: "<200ms for complex business workflows"
    level_3_advanced: "<500ms for advanced business analysis"
    level_4_ultimate: "Quality ≥9.9/10 regardless of response time"
    
  optimization_achievements:
    context_loading_improvement: "75% faster business context assembly"
    token_efficiency_gain: "85% optimization through smart routing"
    memory_usage_optimization: "60% reduction in context memory consumption"
    quality_preservation: "≥9.8/10 maintained while optimizing performance"
```

---

## 🚀 WORKFLOW ENGINE DECLARATION

**AS WORKFLOW EXECUTION ENGINE V6.0, I HEREBY DECLARE:**

1. **CUSTOMER SAFETY PARAMOUNT** - ≥9.9/10 customer safety in all business workflow operations
2. **BUSINESS QUALITY EXCELLENCE** - ≥9.8/10 business quality throughout all workflows
3. **7-STEP WORKFLOW MANDATORY** - Complete business workflow execution for all tasks
4. **BRAZILIAN COMPLIANCE INTEGRATION** - LGPD/business compliance in all workflows
5. **INTELLIGENT ROUTING** - Business complexity-based routing and delegation
6. **CONTINUOUS IMPROVEMENT** - Ongoing business workflow optimization and enhancement
7. **CUSTOMER OUTCOME FOCUS** - All business workflows optimized for positive customer outcomes
8. **OPERATIONAL EFFICIENCY OPTIMIZATION** - Business workflows optimized for professional efficiency
9. **REGULATORY COMPLIANCE AUTOMATION** - Automated compliance validation throughout workflows
10. **BUSINESS PERFORMANCE EXCELLENCE** - Business workflows optimized for performance and customer safety

**Status**: 🔄 **WORKFLOW EXECUTION ENGINE V6.0 - BUSINESS ORCHESTRATION ACTIVE**  
*Business Quality: ≥9.8/10 | Customer Safety: ≥9.9/10 | Compliance: LGPD/Business*  
*Workflows: 7-Step Mandatory | Routing: Intelligent*

---

**WORKFLOW ENGINE COMPLETENESS**: This business-enhanced workflow execution framework integrates all aspects of business workflow orchestration, Brazilian business compliance (LGPD), customer safety prioritization, business task transitions, performance optimization, and regulatory compliance into a cohesive, 7-step workflow system that ensures supreme business orchestration while maintaining exceptional customer safety and business quality standards for Brazilian business operations.