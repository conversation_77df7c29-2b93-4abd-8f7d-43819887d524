{"// VIBECODE V6.0 - Configurações de Orquestração Modular": "Coordenação copilot-instructions.md ↔ voidbeast-modular.chatmode.md", "// GitHub Copilot Settings - Modular Integration": "Core GitHub Copilot configuration for intelligent orchestration", "github.copilot.chat.codeGeneration.useInstructionFiles": true, "github.copilot.chat.codeGeneration.instructions": [{"text": "Always follow VIBECODE V6.0 modular orchestration protocols", "file": ".github/copilot-instructions.md"}], "// Chat & Instructions File Locations": "Modular context injection configuration", "chat.instructionsFilesLocations": {".github/instructions": true, ".github/rules/core": true, ".github/rules/workflows": false}, "// Chat Mode Files": "Specialized orchestrator configuration", "chat.modeFilesLocations": {".github/chatmodes": true}, "// Prompt Files": "Reusable prompt templates", "chat.promptFilesLocations": {".github/prompts": true}, "// Performance & Quality Settings": "Optimization and quality enforcement", "github.copilot.chat.followUps": true, "github.copilot.chat.scopeSelection": true, "github.copilot.enable": {"*": true}, "// Experimental Features": "Advanced GitHub Copilot features for enhanced orchestration", "chat.agent.enabled": true, "chat.agent.maxRequests": 25, "github.copilot.chat.agent.autoFix": true, "github.copilot.chat.agent.runTasks": true, "chat.implicitContext.enabled": true, "github.copilot.chat.edits.temporalContext.enabled": true, "// MCP Support": "Model Context Protocol integration", "chat.mcp.enabled": true, "// Performance Optimization": "Context and performance settings", "editor.inlineSuggest.fontFamily": "Fira Code, Consolas, monospace", "editor.inlineSuggest.showToolbar": true, "editor.inlineSuggest.syntaxHighlightingEnabled": true, "github.copilot.nextEditSuggestions.enabled": true, "editor.inlineSuggest.edits.allowCodeShifting": true, "editor.inlineSuggest.edits.renderSideBySide": "auto"}